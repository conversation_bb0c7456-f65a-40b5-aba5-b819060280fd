<?php
/**
 * Professional DTF Gang Sheet Builder - PHP Version
 * Enhanced with server-side processing and database integration
 */

// Include configuration and database abstraction
require_once 'dtf-working-copy/includes/config.php';
require_once 'dtf-working-copy/includes/database-abstraction.php';
require_once 'dtf-working-copy/includes/functions.php';

// Initialize session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get or create user
$user = dtf_get_or_create_user();

// Get user projects for quick access
$recent_projects = [];
if ($user) {
    try {
        $db = dtf_db();
        $recent_projects = $db->fetchAll(
            'projects',
            ['user_id' => $user['id']],
            'updated_at DESC',
            5
        );
    } catch (Exception $e) {
        dtf_log('ERROR', 'Failed to load recent projects', ['error' => $e->getMessage()]);
    }
}

// Page title and meta
$page_title = "Professional DTF Gang Sheet Builder";
$page_description = "Industry-standard DTF gang sheet creation with auto-nesting and professional features";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Professional DTF Builder Styles -->
    <link rel="stylesheet" href="dtf-working-copy/assets/css/professional-builder.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Fabric.js for advanced canvas manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 10px;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header p {
            margin: 0;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .header-info {
            text-align: left;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .header-actions a {
            color: #00d4ff;
            text-decoration: none;
            font-size: 0.9rem;
            padding: 8px 15px;
            border: 1px solid #00d4ff;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .header-actions a:hover {
            background: #00d4ff;
            color: #2c3e50;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 0;
            height: calc(100vh - 80px);
            width: 100%;
        }

        /* Collapsible Navigation Sidebar */
        .nav-sidebar {
            background: #2c3e50;
            color: white;
            overflow-y: auto;
            transition: all 0.3s ease;
            border-radius: 10px 0 0 10px;
        }

        .nav-sidebar.collapsed {
            width: 60px;
        }

        .nav-header {
            padding: 15px 20px;
            background: #34495e;
            border-bottom: 1px solid #3d566e;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-brand {
            font-weight: 600;
            font-size: 1.1rem;
            color: #00d4ff;
        }

        .nav-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .nav-toggle:hover {
            background: rgba(255,255,255,0.1);
        }

        .nav-section {
            border-bottom: 1px solid #3d566e;
        }

        .nav-section-header {
            padding: 15px 20px;
            background: #34495e;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background 0.3s ease;
            user-select: none;
        }

        .nav-section-header:hover {
            background: #3d566e;
        }

        .nav-section-header.active {
            background: #00d4ff;
            color: #2c3e50;
        }

        .nav-section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .nav-section-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .nav-section-arrow {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .nav-section-arrow.expanded {
            transform: rotate(90deg);
        }

        .nav-section-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #2c3e50;
        }

        .nav-section-content.expanded {
            max-height: 500px;
        }

        .nav-section-content .control-section {
            padding: 20px;
            border-bottom: none;
            margin-bottom: 0;
        }

        .canvas-area {
            background: white;
            border-radius: 0 8px 8px 0;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            width: 100%;
            min-height: calc(100vh - 80px);
        }

        /* Collapsed sidebar styles */
        .nav-sidebar.collapsed {
            width: 60px;
        }

        .nav-sidebar.collapsed .nav-section-title span,
        .nav-sidebar.collapsed .nav-section-arrow,
        .nav-sidebar.collapsed .nav-brand {
            display: none;
        }

        .nav-sidebar.collapsed .nav-section-content {
            display: none;
        }

        .nav-sidebar.collapsed .nav-section-header {
            padding: 15px 10px;
            justify-content: center;
            position: relative;
        }

        .nav-sidebar.collapsed .nav-header {
            padding: 15px 10px;
            justify-content: center;
        }

        /* Canvas-style tooltips for collapsed sidebar */
        .nav-sidebar.collapsed .nav-section-header::before {
            content: attr(data-tooltip);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(4px);
        }

        .nav-sidebar.collapsed .nav-section-header:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Canva-style popup panels - positioned right next to icons */
        .nav-section-popup {
            position: absolute;
            left: 60px; /* Right next to the 60px collapsed sidebar */
            top: 0;
            width: 320px;
            max-height: 500px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .nav-sidebar.collapsed .nav-section-header:hover .nav-section-popup {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .nav-section-popup .popup-header {
            padding: 15px 18px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-section-popup .popup-header::before {
            content: '';
            width: 3px;
            height: 16px;
            background: #3498db;
            border-radius: 2px;
        }

        .nav-section-popup .control-section {
            padding: 18px;
            border-bottom: none;
            margin-bottom: 0;
            background: white;
        }

        /* Hide tooltip when popup is shown */
        .nav-sidebar.collapsed .nav-section-header:hover::before {
            display: none;
        }

        /* Popup arrow indicator pointing from icon */
        .nav-section-popup::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 20px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-right: 6px solid white;
            filter: drop-shadow(-1px 0 2px rgba(0,0,0,0.1));
        }
        /* Professional Controls */
        .control-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .control-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Settings Grid */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 500;
        }

        .upload-zone:hover {
            border-color: #3498db;
            background: #ecf0f1;
            color: #2c3e50;
        }

        .upload-zone small {
            color: #555;
            font-weight: 400;
        }

        .upload-zone.has-files {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .upload-zone.dragover {
            border-color: #3498db;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        /* Image List */
        .image-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
        }

        .image-item:last-child {
            border-bottom: none;
        }

        .image-thumb {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }

        .image-info {
            flex: 1;
        }

        .image-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .image-size {
            color: #7f8c8d;
            font-size: 0.75rem;
        }

        .image-real-size {
            font-size: 0.7rem;
            color: #00d4ff;
            font-weight: 600;
            margin-top: 2px;
        }

        .quantity-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
            font-size: 0.8rem;
        }

        /* Recent Projects */
        .recent-projects {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .project-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
        }

        .project-item:last-child {
            border-bottom: none;
        }

        .project-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .project-date {
            color: #6c757d;
            font-size: 0.75rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
            width: 100%;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .hidden { display: none; }

        /* Canvas */
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
            position: relative;
            overflow: auto; /* Allow scrolling instead of hidden */
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 100%;
            height: calc(100vh - 200px);
            cursor: default;
        }

        .canvas-container.panning {
            cursor: grab;
        }

        .canvas-container.panning:active {
            cursor: grabbing;
        }

        /* Canvas Toolbar */
        .canvas-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .canvas-tools {
            display: flex;
            gap: 10px;
        }

        .tool-button {
            background: white;
            border: 1px solid #ddd;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            color: #333;
        }

        .tool-button:hover {
            background: #f0f0f0;
            border-color: #3498db;
        }

        .tool-button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .zoom-level {
            font-weight: 600;
            color: #333;
        }

        #design-canvas {
            display: block;
            width: 100%;
            height: calc(100vh - 280px);
            background: white;
            cursor: crosshair;
            border: none;
        }

        .canvas-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 0.85rem;
            color: #666;
        }

        /* Status */
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85rem;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Professional Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #2c3e50;
            border-radius: 8px;
        }

        .stat-card {
            background: #34495e;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #4a5f7a;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: #3d566e;
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #bdc3c7;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Navigation specific control styling */
        .nav-section-content .control-input {
            background: #34495e;
            border: 1px solid #4a5f7a;
            color: white;
        }

        .nav-section-content .control-input:focus {
            border-color: #00d4ff;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }

        .nav-section-content .control-label {
            color: #bdc3c7;
            font-size: 0.85rem;
        }

        .nav-section-content .btn {
            margin-bottom: 8px;
        }

        .nav-section-content .checkbox-group {
            margin-bottom: 8px;
        }

        .nav-section-content .checkbox-group label {
            color: #bdc3c7;
        }

        /* Canva-style popup control styling */
        .nav-section-popup .control-input {
            background: white;
            border: 1px solid #ddd;
            color: #333;
            border-radius: 6px;
            padding: 10px 12px;
            font-size: 0.9rem;
        }

        .nav-section-popup .control-input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            outline: none;
        }

        .nav-section-popup .control-label {
            color: #555;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .nav-section-popup .btn {
            margin-bottom: 10px;
            border-radius: 6px;
            font-weight: 500;
            padding: 10px 16px;
        }

        .nav-section-popup .upload-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            color: #2c3e50;
            font-weight: 500;
        }

        .nav-section-popup .upload-zone:hover {
            border-color: #3498db;
            background: #f0f8ff;
            color: #2c3e50;
        }

        .nav-section-popup .upload-zone small {
            color: #555;
            font-weight: 400;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-info">
                <h1>🏭 Professional DTF Gang Sheet Builder</h1>
                <p>Industry-standard features for professional DTF printing</p>
                <?php if ($user): ?>
                    <small style="opacity: 0.6;">Welcome back, User #<?php echo $user['id']; ?> | Session: <?php echo substr($user['session_id'], 0, 8); ?>...</small>
                <?php endif; ?>
            </div>
            <div class="header-actions">
                <a href="#" onclick="window.open('/shop', '_blank')">🛍️ Shop</a>
                <a href="#" onclick="window.open('/services', '_blank')">🔧 Services</a>
                <a href="#" onclick="window.open('/cart', '_blank')">🛒 Cart</a>
            </div>
        </div>

        <div class="main-grid">
            <!-- Collapsible Navigation Sidebar -->
            <div class="nav-sidebar" id="nav-sidebar">
                <!-- Navigation Header -->
                <div class="nav-header">
                    <div class="nav-brand">🏭 CYPTSHOP</div>
                    <button class="nav-toggle" id="nav-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- File Management Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="file-management" data-tooltip="File Management">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-folder"></i>
                            <span>File Management</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-folder"></i>
                                File Management
                            </div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Upload Images</label>
                                    <div id="upload-zone-popup" class="upload-zone" style="margin-bottom: 15px;">
                                        <div style="font-size: 1.1rem; margin-bottom: 5px;">📤 Drop files or click to upload</div>
                                        <small style="color: #666;">PNG, JPG, PDF, AI, EPS files supported</small>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Recent Files</label>
                                    <div style="font-size: 0.85rem; color: #666; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                                        No recent files
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="file-management-content">
                        <div class="control-section">
                            <div id="upload-zone" class="upload-zone">
                                <div>📤 Drop files or click to upload</div>
                                <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                            </div>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">

                            <!-- Quantity Selector - SEPARATE from image list -->
                            <div id="quantity-selector" class="hidden" style="background: #f8f9fa; border: 2px solid #3498db; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 0.9rem;">📊 Quantity Selector</h4>
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                    <label style="font-weight: 600; color: #2c3e50;">Copies:</label>
                                    <input type="number" id="quantity-input" value="1" min="1" max="999"
                                           style="width: 70px; text-align: center; font-size: 1.1rem; padding: 5px; border: 2px solid #3498db; border-radius: 4px; font-weight: 600;">
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button id="duplicate-btn" class="btn btn-success" style="font-size: 0.8rem; padding: 6px 12px;">
                                        📋 Duplicate
                                    </button>
                                    <button id="entire-sheet-btn" class="btn btn-primary" style="font-size: 0.8rem; padding: 6px 12px;">
                                        🔄 Entire Sheet
                                    </button>
                                </div>
                            </div>

                            <!-- File Details Display - Shows after upload -->
                            <div id="file-details" class="hidden" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 12px; margin-bottom: 15px;">
                                <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 0.9rem;">📄 File Details</h4>
                                <div id="file-details-content">
                                    <!-- File details will be populated here -->
                                </div>
                            </div>

                            <div id="image-list" class="image-list hidden"></div>

                            <?php if (!empty($recent_projects)): ?>
                            <div style="margin-top: 15px;">
                                <div class="section-title" style="color: #bdc3c7; font-size: 0.9rem;">Recent Projects</div>
                                <div class="recent-projects">
                                    <?php foreach (array_slice($recent_projects, 0, 3) as $project): ?>
                                    <div class="project-item" style="padding: 8px; border-bottom: 1px solid #3d566e;">
                                        <div>
                                            <div class="project-name" style="color: white; font-size: 0.85rem;"><?php echo htmlspecialchars($project['name']); ?></div>
                                            <div class="project-date" style="color: #95a5a6; font-size: 0.75rem;"><?php echo date('M j', strtotime($project['updated_at'])); ?></div>
                                        </div>
                                        <button class="btn btn-sm" style="padding: 4px 8px; font-size: 0.75rem;" onclick="loadProject(<?php echo $project['id']; ?>)">Load</button>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quantity Control Section -->
                <div class="nav-section" id="quantity-nav-section" style="display: none;">
                    <div class="nav-section-header" data-section="quantity" data-tooltip="Quantity Control">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-copy"></i>
                            <span>Quantity</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-copy"></i>
                                Quantity Control
                            </div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label style="font-weight: 600; color: #2c3e50; margin-bottom: 8px; display: block;">Number of Copies:</label>
                                    <input type="number" id="left-menu-quantity" value="1" min="1" max="999"
                                           style="width: 100%; text-align: center; font-size: 1.2rem; padding: 8px; border: 2px solid #3498db; border-radius: 4px; font-weight: 600; margin-bottom: 10px;">
                                </div>
                                <div class="control-group">
                                    <button id="left-menu-duplicate" class="btn btn-success" style="width: 100%; margin-bottom: 8px;">
                                        📋 Create Copies
                                    </button>
                                    <button id="left-menu-entire-sheet" class="btn btn-primary" style="width: 100%;">
                                        🔄 Fill Entire Sheet
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="quantity-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Number of Copies</label>
                                <input type="number" id="sidebar-quantity-input" value="1" min="1" max="999" class="control-input" style="text-align: center; font-size: 1.1rem; font-weight: 600;">
                            </div>
                            <button id="sidebar-duplicate-btn" class="btn btn-success">
                                📋 Create Copies
                            </button>
                            <button id="sidebar-entire-sheet-btn" class="btn btn-primary">
                                🔄 Fill Entire Sheet
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="sheet-config" data-tooltip="Sheet Configuration">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-ruler-combined"></i>
                            <span>Sheet Configuration</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-ruler-combined"></i>
                                Sheet Configuration
                            </div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Sheet Size</label>
                                    <select class="control-input">
                                        <option>22" × 72" (Popular)</option>
                                        <option>22" × 60"</option>
                                        <option>22" × 48"</option>
                                        <option>Custom Size</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Print Quality</label>
                                    <select class="control-input">
                                        <option>300 DPI (Standard)</option>
                                        <option>600 DPI (High Quality)</option>
                                        <option>1200 DPI (Premium)</option>
                                    </select>
                                </div>
                                <div style="font-size: 0.85rem; color: #666; padding: 10px; background: #f0f8ff; border-radius: 6px; border-left: 3px solid #3498db;">
                                    💡 22" × 72" is the most popular size for DTF gang sheets
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="sheet-config-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Sheet Size</label>
                                <select id="sheet-size" class="control-input">
                                    <option value="22x12">22" × 12" (Standard)</option>
                                    <option value="22x24">22" × 24"</option>
                                    <option value="22x36">22" × 36"</option>
                                    <option value="22x48">22" × 48"</option>
                                    <option value="22x60">22" × 60"</option>
                                    <option value="22x72" selected>22" × 72" (Popular)</option>
                                    <option value="22x100">22" × 100"</option>
                                    <option value="22x120">22" × 120" (Max)</option>
                                </select>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">DPI</label>
                                    <select id="dpi" class="control-input">
                                        <option value="150">150 DPI</option>
                                        <option value="300" selected>300 DPI</option>
                                        <option value="600">600 DPI</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Color Mode</label>
                                    <select id="color-mode" class="control-input">
                                        <option value="cmyk" selected>CMYK</option>
                                        <option value="rgb">RGB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="advanced-settings" data-tooltip="Advanced Settings">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-cogs"></i>
                            <span>Advanced Settings</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-cogs"></i>
                                Advanced Settings
                            </div>
                            <div class="control-section">
                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Spacing (mm)</label>
                                        <input type="number" class="control-input" value="3" min="0" max="10">
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Bleed (mm)</label>
                                        <input type="number" class="control-input" value="1" min="0" max="5">
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Optimization</label>
                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox" checked> Auto-rotate for best fit
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox" checked> Maintain aspect ratio
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox"> Add safety margins
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="advanced-settings-content">
                        <div class="control-section">
                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">Spacing (mm)</label>
                                    <input type="number" id="spacing" class="control-input" value="3" min="0" max="20" step="0.5">
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Bleed (mm)</label>
                                    <input type="number" id="bleed" class="control-input" value="1" min="0" max="10" step="0.5">
                                </div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Nesting Algorithm</label>
                                <select id="nesting-algorithm" class="control-input">
                                    <option value="efficiency" selected>Maximum Efficiency</option>
                                    <option value="speed">Fastest Processing</option>
                                    <option value="uniform">Uniform Spacing</option>
                                    <option value="rows">Row-by-Row</option>
                                </select>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="auto-rotate" checked>
                                <label for="auto-rotate">Auto-rotate for optimal fit</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="maintain-aspect" checked>
                                <label for="maintain-aspect">Maintain aspect ratio</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="add-margins" checked>
                                <label for="add-margins">Add safety margins</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="add-crop-marks">
                                <label for="add-crop-marks">Add crop marks</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Features Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="professional-features" data-tooltip="Professional Features">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-star"></i>
                            <span>Professional Features</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Professional Features</div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Export Format</label>
                                    <select class="control-input" style="font-size: 0.8rem;">
                                        <option>PNG (Recommended)</option>
                                        <option>PDF (Print Ready)</option>
                                        <option>TIFF (Professional)</option>
                                    </select>
                                </div>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    Export and project management
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="professional-features-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Export Format</label>
                                <select id="export-format" class="control-input">
                                    <option value="png" selected>PNG (Recommended)</option>
                                    <option value="pdf">PDF (Print Ready)</option>
                                    <option value="jpg">JPEG (Compressed)</option>
                                    <option value="tiff">TIFF (Professional)</option>
                                </select>
                            </div>

                            <button id="save-project-btn" class="btn btn-primary">
                                💾 Save Project
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="quick-actions" data-tooltip="Quick Actions">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-bolt"></i>
                            <span>Quick Actions</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Quick Actions</div>
                            <div class="control-section">
                                <button class="btn btn-primary" style="margin-bottom: 8px; font-size: 0.8rem;">
                                    🧩 Auto-Nest Images
                                </button>
                                <button class="btn btn-primary" style="margin-bottom: 8px; font-size: 0.8rem;">
                                    ⚡ Optimize Layout
                                </button>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    One-click automation tools
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="quick-actions-content">
                        <div class="control-section">
                            <button id="auto-nest-btn" class="btn btn-primary" disabled>
                                🧩 Auto-Nest Images
                            </button>
                            <button id="optimize-btn" class="btn btn-primary" disabled>
                                ⚡ Optimize Layout
                            </button>
                            <button id="preview-btn" class="btn btn-primary" disabled>
                                👁️ Preview Print
                            </button>
                            <button id="auto-fill-sheet-btn" class="btn btn-primary" disabled>
                                🔄 Auto-Fill Sheet
                            </button>
                            <button id="download-btn" class="btn btn-success hidden">
                                📥 Download Gang Sheet
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Add to Cart Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="add-to-cart" data-tooltip="Add to Cart">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-shopping-cart"></i>
                            <span>Add to Cart</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Add to Cart</div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Quantity</label>
                                    <input type="number" class="control-input" value="1" style="font-size: 0.8rem;">
                                </div>
                                <button class="btn btn-success" style="font-size: 0.8rem;">
                                    🛒 Add to Cart - $0.00
                                </button>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    Purchase gang sheet printing
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="add-to-cart-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Quantity</label>
                                <input type="number" id="cart-quantity" class="control-input" value="1" min="1" max="100">
                            </div>
                            <div class="control-group">
                                <label class="control-label">Rush Order</label>
                                <select id="rush-order" class="control-input">
                                    <option value="standard">Standard (3-5 days)</option>
                                    <option value="rush">Rush (1-2 days) +$25</option>
                                    <option value="same-day">Same Day +$50</option>
                                </select>
                            </div>
                            <button id="add-to-cart-btn" class="btn btn-success" disabled>
                                🛒 Add to Cart - $0.00
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <!-- Professional Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="image-count">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Container with Toolbar -->
                <div class="canvas-container">
                    <!-- Canvas Toolbar -->
                    <div class="canvas-toolbar">
                        <div class="canvas-tools">
                            <button id="zoom-in" class="tool-button" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button id="zoom-out" class="tool-button" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button id="zoom-fit" class="tool-button" title="Fit to View">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button id="select-tool" class="tool-button active" title="Select Tool">
                                <i class="fas fa-mouse-pointer"></i>
                            </button>
                            <button id="hand-tool" class="tool-button" title="Hand Tool - Pan Canvas">
                                <i class="fas fa-hand-paper"></i>
                            </button>
                            <button id="move-tool" class="tool-button" title="Move Tool">
                                <i class="fas fa-arrows-alt"></i>
                            </button>
                            <button id="rotate-tool" class="tool-button" title="Rotate Tool">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button id="duplicate-tool" class="tool-button" title="Duplicate Selected">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button id="delete-tool" class="tool-button" title="Delete Selected">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button id="grid-toggle" class="tool-button" title="Toggle Grid">
                                <i class="fas fa-th"></i>
                            </button>
                            <button id="reset-view" class="tool-button" title="Reset Canvas View">
                                <i class="fas fa-home"></i>
                            </button>
                        </div>

                        <div class="zoom-controls">
                            <span>Zoom:</span>
                            <span id="zoom-level" class="zoom-level">100%</span>
                        </div>
                    </div>

                    <!-- Design Canvas with Fabric.js -->
                    <canvas id="design-canvas" width="800" height="600"></canvas>

                    <!-- Canvas Info -->
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">22" × 72"</span></span>
                        <span>DPI: <span id="current-dpi">300</span></span>
                        <span>Status: <span id="canvas-status">Ready</span></span>
                    </div>
                </div>

                <!-- Status Messages -->
                <div id="status-message" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Include Professional DTF Builder JavaScript -->
    <!-- Note: Using inline JavaScript since external files don't exist yet -->

    <script>
        // Initialize Professional DTF Builder with Fabric.js and PHP integration
        class ProfessionalDTFBuilder {
            constructor() {
                this.fabricCanvas = null;
                this.uploadedImages = [];
                this.sheetSize = '22x72';
                this.dpi = 300;
                this.spacing = 3; // mm
                this.bleed = 1; // mm
                this.autoRotate = true;
                this.maintainAspect = true;
                this.addMargins = true;
                this.currentTool = 'select';
                this.zoomLevel = 1;
                this.isGridVisible = true;
                this.isPanning = false;
                this.lastPanPoint = null;
                this.dpi = 300; // Standard DTF DPI
                this.userId = <?php echo json_encode($user['id'] ?? null); ?>;
                this.sessionId = <?php echo json_encode($user['session_id'] ?? null); ?>;

                this.init();
            }

            init() {
                console.log('Initializing DTF Builder...');

                // Check if Fabric.js is loaded
                if (typeof fabric === 'undefined') {
                    console.error('Fabric.js not loaded!');
                    this.showStatus('Error: Fabric.js library not loaded. Please refresh the page.', 'error');
                    return;
                }

                console.log('Fabric.js version:', fabric.version);

                this.initFabricCanvas();
                this.setupEvents();
                this.setupToolbar();
                this.setupNavigation();
                this.updateStats();

                // Set grid toggle button as active by default
                const gridBtn = document.getElementById('grid-toggle');
                if (gridBtn) {
                    gridBtn.classList.add('active');
                }

                this.showStatus('DTF Builder initialized successfully', 'success');

                // Set initial pricing
                this.updatePricing();

                console.log('DTF Builder initialization complete');
            }

            initFabricCanvas() {
                console.log('Initializing Fabric canvas...');
                const canvasElement = document.getElementById('design-canvas');
                if (!canvasElement) {
                    console.error('Canvas element not found');
                    this.showStatus('Error: Canvas element not found', 'error');
                    return;
                }

                console.log('Canvas element found, creating Fabric canvas');

                try {
                    // Calculate exact canvas dimensions based on sheet size and DPI
                    const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();

                    // For display, we'll use a scale factor to fit the canvas in the viewport
                    // But maintain the exact aspect ratio of the sheet
                    const displayScale = this.calculateDisplayScale(sheetWidthInches, sheetHeightInches);

                    const canvasWidth = Math.round(sheetWidthInches * displayScale);
                    const canvasHeight = Math.round(sheetHeightInches * displayScale);

                    console.log(`Creating canvas: ${canvasWidth}x${canvasHeight} (display scale: ${displayScale.toFixed(2)})`);
                    console.log(`Represents sheet: ${sheetWidthInches}" x ${sheetHeightInches}" @ ${this.dpi} DPI`);

                    this.fabricCanvas = new fabric.Canvas('design-canvas', {
                        width: canvasWidth,
                        height: canvasHeight,
                        backgroundColor: '#ffffff',
                        selection: true,
                        preserveObjectStacking: true
                    });

                    // Store the exact print dimensions and scale
                    this.printWidthPixels = sheetWidthInches * this.dpi;
                    this.printHeightPixels = sheetHeightInches * this.dpi;
                    this.displayScale = displayScale;
                    this.pixelsPerInchDisplay = displayScale; // Pixels per inch on display canvas

                    console.log(`Print dimensions: ${this.printWidthPixels}x${this.printHeightPixels} pixels @ ${this.dpi} DPI`);
                    console.log('Fabric canvas created successfully');

                    // Add grid background
                    this.drawGrid();

                    // Canvas event handlers
                    this.fabricCanvas.on('selection:created', (e) => {
                        this.updateToolbar();
                        this.showObjectDimensions(e.target);
                    });
                    this.fabricCanvas.on('selection:updated', (e) => {
                        this.updateToolbar();
                        this.showObjectDimensions(e.target);
                    });
                    this.fabricCanvas.on('selection:cleared', () => {
                        this.updateToolbar();
                        this.hideObjectDimensions();
                    });
                    this.fabricCanvas.on('object:modified', (e) => {
                        this.updateStats();
                        if (e.target && e.target.isExactSize) {
                            this.updateObjectRealDimensions(e.target);
                        }
                    });
                    this.fabricCanvas.on('object:added', () => this.updateStats());
                    this.fabricCanvas.on('object:removed', () => this.updateStats());
                    this.fabricCanvas.on('object:moving', (e) => {
                        if (e.target && e.target.isExactSize) {
                            this.showObjectPosition(e.target);
                        }
                    });

                    console.log('Canvas event handlers set up');

                    // Enable grid snapping by default
                    this.enableGridSnapping();

                    // Enable mouse wheel zoom
                    this.enableMouseWheelZoom();

                } catch (error) {
                    console.error('Error creating Fabric canvas:', error);
                    this.showStatus('Error initializing canvas: ' + error.message, 'error');
                }
            }

            calculateDisplayScale(widthInches, heightInches) {
                // Available space for canvas (accounting for UI elements)
                const maxDisplayWidth = 700;  // Max width in viewport
                const maxDisplayHeight = 450; // Max height in viewport

                console.log(`Calculating scale for ${widthInches}" x ${heightInches}"`);
                console.log(`Available display area: ${maxDisplayWidth}x${maxDisplayHeight}`);

                // Calculate scale to fit within display area while maintaining aspect ratio
                const scaleX = maxDisplayWidth / widthInches;
                const scaleY = maxDisplayHeight / heightInches;

                console.log(`Scale options: X=${scaleX.toFixed(2)}, Y=${scaleY.toFixed(2)}`);

                // Use the smaller scale to ensure it fits completely
                let scale = Math.min(scaleX, scaleY);

                // Ensure reasonable bounds for visibility and performance
                scale = Math.max(scale, 15); // Minimum 15 pixels per inch
                scale = Math.min(scale, 80); // Maximum 80 pixels per inch

                console.log(`Final display scale: ${scale.toFixed(2)} pixels per inch`);
                console.log(`Resulting canvas: ${(widthInches * scale).toFixed(0)}x${(heightInches * scale).toFixed(0)}`);

                return scale;
            }

            drawGrid() {
                if (!this.isGridVisible || !this.fabricCanvas) return;

                console.log('Drawing PRECISE grid for sheet size:', this.sheetSize, 'DPI:', this.dpi);

                const canvas = this.fabricCanvas;
                const canvasWidth = canvas.getWidth();
                const canvasHeight = canvas.getHeight();

                // Clear existing grid
                const existingGrid = canvas.getObjects().filter(obj => obj.isGrid);
                existingGrid.forEach(obj => canvas.remove(obj));

                // Get sheet dimensions in inches
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                console.log('EXACT Sheet dimensions:', sheetWidthInches, 'x', sheetHeightInches, 'inches');
                console.log('Print resolution:', this.printWidthPixels, 'x', this.printHeightPixels, 'pixels @', this.dpi, 'DPI');

                // The canvas now represents the EXACT sheet dimensions
                // Each pixel on canvas corresponds to exact measurements
                const pixelsPerInch = this.displayScale;
                console.log('Display pixels per inch:', pixelsPerInch);

                // Canvas fills the entire sheet area - no offset needed
                const sheetCanvasWidth = canvasWidth;
                const sheetCanvasHeight = canvasHeight;

                // Draw sheet boundary (the entire canvas)
                const sheetBoundary = new fabric.Rect({
                    left: 0,
                    top: 0,
                    width: sheetCanvasWidth,
                    height: sheetCanvasHeight,
                    fill: 'transparent',
                    stroke: '#e74c3c',
                    strokeWidth: 2,
                    selectable: false,
                    evented: false,
                    isGrid: true,
                    strokeDashArray: [8, 4]
                });
                canvas.add(sheetBoundary);
                canvas.sendToBack(sheetBoundary);

                // Add corner markers to show exact sheet boundaries
                const cornerSize = 10;
                const corners = [
                    {x: 0, y: 0}, // Top-left
                    {x: sheetCanvasWidth, y: 0}, // Top-right
                    {x: 0, y: sheetCanvasHeight}, // Bottom-left
                    {x: sheetCanvasWidth, y: sheetCanvasHeight} // Bottom-right
                ];

                corners.forEach(corner => {
                    const marker = new fabric.Circle({
                        left: corner.x - cornerSize/2,
                        top: corner.y - cornerSize/2,
                        radius: cornerSize/2,
                        fill: '#e74c3c',
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.8
                    });
                    canvas.add(marker);
                    canvas.sendToBack(marker);
                });

                // Draw PRECISE 1-inch grid lines
                const gridSpacing = pixelsPerInch; // Exact 1 inch spacing

                // Vertical grid lines (every inch)
                for (let i = 1; i < sheetWidthInches; i++) {
                    const x = i * gridSpacing;
                    const line = new fabric.Line([x, 0, x, sheetCanvasHeight], {
                        stroke: '#3498db',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.5
                    });
                    canvas.add(line);
                    canvas.sendToBack(line);
                }

                // Horizontal grid lines (every inch)
                for (let i = 1; i < sheetHeightInches; i++) {
                    const y = i * gridSpacing;
                    const line = new fabric.Line([0, y, sheetCanvasWidth, y], {
                        stroke: '#3498db',
                        strokeWidth: 1,
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.5
                    });
                    canvas.add(line);
                    canvas.sendToBack(line);
                }

                // Draw major grid lines every 5 inches with measurements
                for (let i = 5; i < sheetWidthInches; i += 5) {
                    const x = i * pixelsPerInch;
                    const line = new fabric.Line([x, 0, x, sheetCanvasHeight], {
                        stroke: '#2980b9',
                        strokeWidth: 2,
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.7
                    });
                    canvas.add(line);
                    canvas.sendToBack(line);

                    // Add measurement label
                    const label = new fabric.Text(`${i}"`, {
                        left: x - 8,
                        top: 5,
                        fontSize: 11,
                        fill: '#2980b9',
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.8,
                        fontWeight: 'bold'
                    });
                    canvas.add(label);
                    canvas.sendToBack(label);
                }

                for (let i = 5; i < sheetHeightInches; i += 5) {
                    const y = i * pixelsPerInch;
                    const line = new fabric.Line([0, y, sheetCanvasWidth, y], {
                        stroke: '#2980b9',
                        strokeWidth: 2,
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.7
                    });
                    canvas.add(line);
                    canvas.sendToBack(line);

                    // Add measurement label
                    const label = new fabric.Text(`${i}"`, {
                        left: 5,
                        top: y - 6,
                        fontSize: 11,
                        fill: '#2980b9',
                        selectable: false,
                        evented: false,
                        isGrid: true,
                        opacity: 0.8,
                        fontWeight: 'bold'
                    });
                    canvas.add(label);
                    canvas.sendToBack(label);
                }

                // Add dimension labels at corners
                const dimensionLabel = new fabric.Text(`${sheetWidthInches}" × ${sheetHeightInches}"`, {
                    left: sheetCanvasWidth - 80,
                    top: sheetCanvasHeight - 25,
                    fontSize: 12,
                    fill: '#e74c3c',
                    selectable: false,
                    evented: false,
                    isGrid: true,
                    opacity: 0.9,
                    fontWeight: 'bold',
                    backgroundColor: 'rgba(255,255,255,0.8)'
                });
                canvas.add(dimensionLabel);
                canvas.sendToBack(dimensionLabel);

                canvas.renderAll();
                console.log('PRECISE grid drawn - Each grid square = 1 inch exactly');
                console.log('Canvas represents exact printable area');
            }

            getSheetDimensions() {
                // Parse sheet size (e.g., "22x72" -> [22, 72])
                const [width, height] = this.sheetSize.split('x').map(Number);
                return [width, height];
            }

            updateCanvasSize() {
                if (!this.fabricCanvas) return;

                const [sheetWidth, sheetHeight] = this.getSheetDimensions();
                console.log('Updating canvas to EXACT sheet size:', sheetWidth, 'x', sheetHeight, 'inches');

                // Calculate new exact canvas dimensions
                const displayScale = this.calculateDisplayScale(sheetWidth, sheetHeight);
                const newCanvasWidth = Math.round(sheetWidth * displayScale);
                const newCanvasHeight = Math.round(sheetHeight * displayScale);

                console.log(`Resizing canvas to: ${newCanvasWidth}x${newCanvasHeight} (scale: ${displayScale.toFixed(2)})`);

                // Update canvas dimensions
                this.fabricCanvas.setDimensions({
                    width: newCanvasWidth,
                    height: newCanvasHeight
                });

                // Update exact print dimensions
                this.printWidthPixels = sheetWidth * this.dpi;
                this.printHeightPixels = sheetHeight * this.dpi;
                this.displayScale = displayScale;
                this.pixelsPerInchDisplay = displayScale;

                console.log(`Print dimensions updated: ${this.printWidthPixels}x${this.printHeightPixels} pixels @ ${this.dpi} DPI`);

                // Update UI displays
                document.getElementById('sheet-dimensions').textContent = `${sheetWidth}" × ${sheetHeight}"`;
                document.getElementById('current-dpi').textContent = this.dpi;
                document.getElementById('canvas-status').textContent = `${sheetWidth}" × ${sheetHeight}" @ ${this.dpi} DPI (${this.printWidthPixels}×${this.printHeightPixels}px)`;

                // Store canvas properties for grid calculations
                this.canvasSheetWidth = sheetWidth;
                this.canvasSheetHeight = sheetHeight;

                // Redraw grid with new EXACT dimensions
                this.drawGrid();
                this.updateStats();

                // Update grid snapping for new dimensions
                this.enableGridSnapping();

                console.log('Canvas size updated to EXACT sheet dimensions');
            }

            recreateCanvasForNewSize() {
                console.log('RECREATING canvas for new sheet size:', this.sheetSize);

                if (!this.fabricCanvas) {
                    console.error('No canvas to recreate');
                    return;
                }

                // Store current objects (excluding grid)
                const currentObjects = this.fabricCanvas.getObjects().filter(obj => !obj.isGrid);
                console.log('Preserving', currentObjects.length, 'objects');

                // Get new exact dimensions
                const [newSheetWidth, newSheetHeight] = this.getSheetDimensions();
                const newDisplayScale = this.calculateDisplayScale(newSheetWidth, newSheetHeight);
                const newCanvasWidth = Math.round(newSheetWidth * newDisplayScale);
                const newCanvasHeight = Math.round(newSheetHeight * newDisplayScale);

                console.log(`NEW Canvas dimensions: ${newCanvasWidth}x${newCanvasHeight}`);
                console.log(`NEW Sheet: ${newSheetWidth}" x ${newSheetHeight}"`);
                console.log(`NEW Display scale: ${newDisplayScale.toFixed(2)} pixels/inch`);

                // Clear the canvas completely
                this.fabricCanvas.clear();

                // Set new canvas dimensions
                this.fabricCanvas.setDimensions({
                    width: newCanvasWidth,
                    height: newCanvasHeight
                });

                // Update the HTML canvas element size
                const canvasElement = this.fabricCanvas.getElement();
                canvasElement.style.width = newCanvasWidth + 'px';
                canvasElement.style.height = newCanvasHeight + 'px';

                // Update all dimension properties
                this.printWidthPixels = newSheetWidth * this.dpi;
                this.printHeightPixels = newSheetHeight * this.dpi;
                this.displayScale = newDisplayScale;
                this.pixelsPerInchDisplay = newDisplayScale;
                this.canvasSheetWidth = newSheetWidth;
                this.canvasSheetHeight = newSheetHeight;

                console.log(`Print dimensions: ${this.printWidthPixels}x${this.printHeightPixels} @ ${this.dpi} DPI`);

                // Update UI displays
                document.getElementById('sheet-dimensions').textContent = `${newSheetWidth}" × ${newSheetHeight}"`;
                document.getElementById('current-dpi').textContent = this.dpi;
                document.getElementById('canvas-status').textContent = `${newSheetWidth}" × ${newSheetHeight}" @ ${this.dpi} DPI`;

                // Draw the new grid FIRST
                this.drawGrid();

                // Restore objects with scaling if needed
                currentObjects.forEach(obj => {
                    // Scale objects proportionally if needed
                    const scaleFactorX = newCanvasWidth / (this.previousCanvasWidth || newCanvasWidth);
                    const scaleFactorY = newCanvasHeight / (this.previousCanvasHeight || newCanvasHeight);

                    if (this.previousCanvasWidth && this.previousCanvasHeight) {
                        obj.set({
                            left: obj.left * scaleFactorX,
                            top: obj.top * scaleFactorY,
                            scaleX: obj.scaleX * scaleFactorX,
                            scaleY: obj.scaleY * scaleFactorY
                        });
                    }

                    this.fabricCanvas.add(obj);
                });

                // Store current dimensions for next resize
                this.previousCanvasWidth = newCanvasWidth;
                this.previousCanvasHeight = newCanvasHeight;

                // Re-enable grid snapping
                this.enableGridSnapping();

                // Render everything
                this.fabricCanvas.renderAll();
                this.updateStats();

                console.log('Canvas RECREATED successfully with new exact dimensions');

                // Update pricing based on new sheet size
                this.updatePricing();
            }

            updatePricing() {
                const [width, height] = this.getSheetDimensions();
                const area = width * height; // Square inches

                // DTF pricing tiers based on sheet size
                const pricingTiers = {
                    '22x12': { basePrice: 15.00, area: 264 },
                    '22x24': { basePrice: 25.00, area: 528 },
                    '22x36': { basePrice: 35.00, area: 792 },
                    '22x48': { basePrice: 45.00, area: 1056 },
                    '22x60': { basePrice: 55.00, area: 1320 },
                    '22x72': { basePrice: 65.00, area: 1584 },
                    '22x100': { basePrice: 85.00, area: 2200 },
                    '22x120': { basePrice: 100.00, area: 2640 }
                };

                const pricing = pricingTiers[this.sheetSize] || { basePrice: 0, area: area };
                const rushOrder = document.getElementById('rush-order')?.value || 'standard';

                let finalPrice = pricing.basePrice;
                if (rushOrder === 'rush') finalPrice += 25;
                if (rushOrder === 'same-day') finalPrice += 50;

                const quantity = parseInt(document.getElementById('cart-quantity')?.value || 1);
                const totalPrice = finalPrice * quantity;

                // Update cart button
                const cartBtn = document.getElementById('add-to-cart-btn');
                if (cartBtn) {
                    cartBtn.textContent = `🛒 Add to Cart - $${totalPrice.toFixed(2)}`;
                }

                console.log(`Pricing updated: ${this.sheetSize} = $${finalPrice.toFixed(2)} x ${quantity} = $${totalPrice.toFixed(2)}`);
            }

            // Grid snapping functionality
            snapToGrid(value, gridSize) {
                return Math.round(value / gridSize) * gridSize;
            }

            getGridSpacing() {
                // Return the exact pixels per inch for precise snapping
                return this.displayScale || 20;
            }

            enableGridSnapping() {
                if (!this.fabricCanvas) return;

                // Remove existing event listeners to avoid duplicates
                this.fabricCanvas.off('object:moving');
                this.fabricCanvas.off('object:scaling');

                const gridSpacing = this.getGridSpacing();
                console.log('Grid snapping enabled with spacing:', gridSpacing, 'pixels per inch');

                // Snap to 1-inch grid while moving
                this.fabricCanvas.on('object:moving', (e) => {
                    const obj = e.target;
                    if (obj.isGrid) return; // Don't snap grid objects

                    obj.set({
                        left: this.snapToGrid(obj.left, gridSpacing),
                        top: this.snapToGrid(obj.top, gridSpacing)
                    });
                });

                // Snap to grid while scaling
                this.fabricCanvas.on('object:scaling', (e) => {
                    const obj = e.target;
                    if (obj.isGrid) return;

                    // Snap the scaled dimensions to grid
                    const scaledWidth = obj.width * obj.scaleX;
                    const scaledHeight = obj.height * obj.scaleY;

                    const snappedWidth = this.snapToGrid(scaledWidth, gridSpacing);
                    const snappedHeight = this.snapToGrid(scaledHeight, gridSpacing);

                    obj.set({
                        scaleX: snappedWidth / obj.width,
                        scaleY: snappedHeight / obj.height
                    });
                });
            }

            setupEvents() {
                console.log('Setting up events...');

                // File upload handling
                const uploadZone = document.getElementById('upload-zone');
                const uploadZonePopup = document.getElementById('upload-zone-popup');
                const fileInput = document.getElementById('file-input');

                console.log('Upload elements found:', {
                    uploadZone: !!uploadZone,
                    uploadZonePopup: !!uploadZonePopup,
                    fileInput: !!fileInput
                });

                if (uploadZone && fileInput) {
                    console.log('Setting up main upload zone events');
                    uploadZone.addEventListener('click', () => {
                        console.log('Upload zone clicked, opening file dialog');
                        fileInput.click();
                    });

                    uploadZone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZone.classList.add('dragover');
                        console.log('Drag over upload zone');
                    });

                    uploadZone.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZone.classList.remove('dragover');
                        console.log('Drag leave upload zone');
                    });

                    uploadZone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZone.classList.remove('dragover');
                        console.log('Files dropped:', e.dataTransfer.files);
                        this.handleFiles(e.dataTransfer.files);
                    });

                    fileInput.addEventListener('change', (e) => {
                        console.log('File input changed:', e.target.files);
                        this.handleFiles(e.target.files);
                    });
                } else {
                    console.error('Upload zone or file input not found!');
                }

                // Popup upload zone
                if (uploadZonePopup && fileInput) {
                    console.log('Setting up popup upload zone events');
                    uploadZonePopup.addEventListener('click', () => {
                        console.log('Popup upload zone clicked');
                        fileInput.click();
                    });

                    uploadZonePopup.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZonePopup.classList.add('dragover');
                    });

                    uploadZonePopup.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZonePopup.classList.remove('dragover');
                    });

                    uploadZonePopup.addEventListener('drop', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        uploadZonePopup.classList.remove('dragover');
                        console.log('Files dropped on popup:', e.dataTransfer.files);
                        this.handleFiles(e.dataTransfer.files);
                    });
                }

                // Settings change handlers
                document.getElementById('sheet-size')?.addEventListener('change', (e) => {
                    console.log('Sheet size changed to:', e.target.value);
                    const oldSize = this.sheetSize;
                    this.sheetSize = e.target.value;

                    console.log('OLD sheet size:', oldSize);
                    console.log('NEW sheet size:', this.sheetSize);

                    // FORCE complete canvas recreation for new dimensions
                    this.recreateCanvasForNewSize();
                    this.showStatus(`Sheet size changed to ${this.getSheetDimensions().join('" × ')}"`, 'success');
                });

                document.getElementById('dpi')?.addEventListener('change', (e) => {
                    console.log('DPI changed to:', e.target.value);
                    this.dpi = parseInt(e.target.value);
                    this.drawGrid(); // Redraw grid with new DPI
                    this.updateStats();
                    this.showStatus(`DPI changed to ${this.dpi}`, 'success');
                });

                document.getElementById('spacing')?.addEventListener('input', (e) => {
                    this.spacing = parseFloat(e.target.value);
                    console.log('Spacing changed to:', this.spacing);
                });

                document.getElementById('bleed')?.addEventListener('input', (e) => {
                    this.bleed = parseFloat(e.target.value);
                    console.log('Bleed changed to:', this.bleed);
                });

                // Checkbox handlers
                document.getElementById('auto-rotate')?.addEventListener('change', (e) => {
                    this.autoRotate = e.target.checked;
                });

                document.getElementById('maintain-aspect')?.addEventListener('change', (e) => {
                    this.maintainAspect = e.target.checked;
                });

                document.getElementById('add-margins')?.addEventListener('change', (e) => {
                    this.addMargins = e.target.checked;
                });

                // Pricing update handlers
                document.getElementById('cart-quantity')?.addEventListener('change', () => {
                    this.updatePricing();
                });

                document.getElementById('rush-order')?.addEventListener('change', () => {
                    this.updatePricing();
                });
            }

            handleFiles(files) {
                console.log('Handling files:', files);
                if (!files || files.length === 0) {
                    console.log('No files to handle');
                    return;
                }

                Array.from(files).forEach(file => {
                    console.log('Processing file:', file.name, 'Type:', file.type);
                    if (file.type.startsWith('image/')) {
                        this.loadImage(file);
                    } else {
                        console.log('Unsupported file type:', file.type);
                        this.showStatus(`Unsupported file type: ${file.name}`, 'error');
                    }
                });
            }

            loadImage(file) {
                console.log('Loading image with EXACT dimensions:', file.name);

                if (!this.fabricCanvas) {
                    console.error('Fabric canvas not initialized');
                    this.showStatus('Canvas not ready. Please refresh the page.', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('File read successfully');
                    const img = new Image();
                    img.onload = () => {
                        console.log('Image pixel dimensions:', img.width, 'x', img.height, 'pixels');

                        // Calculate EXACT real-world dimensions in inches at current DPI
                        const realWidthInches = img.width / this.dpi;
                        const realHeightInches = img.height / this.dpi;

                        console.log('EXACT real dimensions:', realWidthInches.toFixed(3), 'x', realHeightInches.toFixed(3), 'inches');
                        console.log('At DPI:', this.dpi);

                        // Calculate exact display size on canvas (pixels on screen)
                        const displayWidthPixels = realWidthInches * this.displayScale;
                        const displayHeightPixels = realHeightInches * this.displayScale;

                        console.log('Display size on canvas:', displayWidthPixels.toFixed(1), 'x', displayHeightPixels.toFixed(1), 'pixels');

                        try {
                            // Calculate safe position within sheet boundaries
                            const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                            const maxLeftPixels = Math.max(0, (sheetWidthInches * this.displayScale) - displayWidthPixels);
                            const maxTopPixels = Math.max(0, (sheetHeightInches * this.displayScale) - displayHeightPixels);

                            // Position at top-left of sheet, ensuring it fits
                            const safeLeft = Math.min(this.displayScale, maxLeftPixels);
                            const safeTop = Math.min(this.displayScale, maxTopPixels);

                            console.log(`Sheet: ${sheetWidthInches}" × ${sheetHeightInches}", Image: ${realWidthInches.toFixed(3)}" × ${realHeightInches.toFixed(3)}"`);
                            console.log(`Safe position: (${(safeLeft/this.displayScale).toFixed(3)}", ${(safeTop/this.displayScale).toFixed(3)}") pixels: (${safeLeft}, ${safeTop})`);

                            // Create fabric image at EXACT real-world size
                            const fabricImg = new fabric.Image(img, {
                                left: safeLeft,
                                top: safeTop,
                                width: img.width,
                                height: img.height,
                                // Scale to exact real-world size on display
                                scaleX: displayWidthPixels / img.width,
                                scaleY: displayHeightPixels / img.height,
                                cornerColor: '#3498db',
                                cornerSize: 8,
                                transparentCorners: false,
                                borderColor: '#3498db',
                                strokeWidth: 2,
                                stroke: '#3498db'
                            });

                            // Add comprehensive metadata
                            fabricImg.originalWidth = img.width;
                            fabricImg.originalHeight = img.height;
                            fabricImg.realWidthInches = realWidthInches;
                            fabricImg.realHeightInches = realHeightInches;
                            fabricImg.displayWidthPixels = displayWidthPixels;
                            fabricImg.displayHeightPixels = displayHeightPixels;
                            fabricImg.fileName = file.name;
                            fabricImg.fileDPI = this.dpi;
                            fabricImg.isExactSize = true;

                            // Add to canvas
                            this.fabricCanvas.add(fabricImg);
                            this.fabricCanvas.setActiveObject(fabricImg);
                            this.fabricCanvas.renderAll();

                            // Add to uploaded images list with exact measurements
                            this.uploadedImages.push({
                                name: file.name,
                                width: img.width,
                                height: img.height,
                                realWidthInches: realWidthInches.toFixed(3),
                                realHeightInches: realHeightInches.toFixed(3),
                                displayWidthPixels: displayWidthPixels.toFixed(1),
                                displayHeightPixels: displayHeightPixels.toFixed(1),
                                fabricObject: fabricImg,
                                quantity: 1,
                                dpi: this.dpi
                            });

                            this.updateImageList();
                            this.updateStats();
                            this.enableButtons();

                            // Show exact dimensions in status
                            this.showStatus(`"${file.name}" loaded at EXACT size: ${realWidthInches.toFixed(3)}" × ${realHeightInches.toFixed(3)}" (${img.width}×${img.height}px @ ${this.dpi} DPI)`, 'success');
                            console.log('Image placed at EXACT real-world size on grid');

                            // Snap to grid for precise positioning
                            const gridSpacing = this.getGridSpacing();
                            fabricImg.set({
                                left: this.snapToGrid(fabricImg.left, gridSpacing),
                                top: this.snapToGrid(fabricImg.top, gridSpacing)
                            });
                            this.fabricCanvas.renderAll();

                            // Show LEFT MENU quantity selector when first image is uploaded
                            this.showLeftMenuQuantitySelector();

                            // Update file details display
                            this.updateFileDetails(file.name, img.width, img.height, realWidthInches, realHeightInches, this.dpi);

                        } catch (error) {
                            console.error('Error creating fabric image:', error);
                            this.showStatus(`Error loading image: ${file.name}`, 'error');
                        }
                    };

                    img.onerror = () => {
                        console.error('Error loading image');
                        this.showStatus(`Error loading image: ${file.name}`, 'error');
                    };

                    img.src = e.target.result;
                };

                reader.onerror = () => {
                    console.error('Error reading file');
                    this.showStatus(`Error reading file: ${file.name}`, 'error');
                };

                reader.readAsDataURL(file);
            }

            updateImageList() {
                const imageList = document.getElementById('image-list');
                if (!imageList) return;

                if (this.uploadedImages.length === 0) {
                    imageList.classList.add('hidden');
                    this.hideQuantitySelector();
                    this.hideFileDetails();
                    this.hideLeftMenuQuantitySelector();
                    return;
                }

                imageList.classList.remove('hidden');
                imageList.innerHTML = '';

                this.uploadedImages.forEach((img, index) => {
                    const item = document.createElement('div');
                    item.className = 'image-item';

                    // Calculate area for reference
                    const areaInches = (parseFloat(img.realWidthInches) * parseFloat(img.realHeightInches)).toFixed(2);

                    item.innerHTML = `
                        <div class="image-info">
                            <div class="image-name" style="font-weight: 600; color: #2c3e50;">${img.name}</div>
                            <div class="image-size" style="color: #7f8c8d; font-size: 0.75rem;">
                                📐 ${img.width} × ${img.height} pixels
                            </div>
                            <div class="image-real-size" style="color: #e74c3c; font-weight: 600; font-size: 0.8rem;">
                                📏 ${img.realWidthInches}" × ${img.realHeightInches}"
                            </div>
                            <div style="color: #3498db; font-size: 0.7rem; margin-top: 2px;">
                                📊 ${areaInches} sq in @ ${img.dpi || this.dpi} DPI
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: center; gap: 6px;">
                            <button onclick="window.dtfBuilder.selectImageOnCanvas(${index})"
                                    style="font-size: 0.8rem; padding: 4px 8px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; width: 60px;">
                                Select
                            </button>
                            <button onclick="window.dtfBuilder.removeImageFromCanvas(${index})"
                                    style="font-size: 0.8rem; padding: 4px 8px; background: #e74c3c; color: white; border: none; border-radius: 3px; cursor: pointer; width: 60px;">
                                Remove
                            </button>
                        </div>
                    `;
                    imageList.appendChild(item);
                });
            }

            showQuantitySelector() {
                const quantitySelector = document.getElementById('quantity-selector');
                if (quantitySelector) {
                    quantitySelector.classList.remove('hidden');
                }
            }

            hideQuantitySelector() {
                const quantitySelector = document.getElementById('quantity-selector');
                if (quantitySelector) {
                    quantitySelector.classList.add('hidden');
                }
            }

            updateFileDetails(fileName, pixelWidth, pixelHeight, realWidthInches, realHeightInches, dpi) {
                const fileDetails = document.getElementById('file-details');
                const fileDetailsContent = document.getElementById('file-details-content');

                if (fileDetails && fileDetailsContent) {
                    fileDetails.classList.remove('hidden');

                    const fileSizeKB = Math.round((pixelWidth * pixelHeight * 3) / 1024); // Rough estimate
                    const areaInches = (realWidthInches * realHeightInches).toFixed(2);

                    fileDetailsContent.innerHTML = `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.8rem;">
                            <div><strong>📁 File:</strong> ${fileName}</div>
                            <div><strong>📐 Pixels:</strong> ${pixelWidth} × ${pixelHeight}</div>
                            <div><strong>📏 Size:</strong> ${realWidthInches.toFixed(3)}" × ${realHeightInches.toFixed(3)}"</div>
                            <div><strong>📊 Area:</strong> ${areaInches} sq in</div>
                            <div><strong>🎯 DPI:</strong> ${dpi}</div>
                            <div><strong>💾 Est. Size:</strong> ~${fileSizeKB} KB</div>
                        </div>
                        <div style="margin-top: 8px; padding: 6px; background: #e8f5e8; border-radius: 4px; font-size: 0.75rem; color: #2d5a2d;">
                            ✅ <strong>PRECISE:</strong> File loaded at exact real-world dimensions for professional DTF printing
                        </div>
                    `;

                    console.log(`File Details Updated: ${fileName} - ${pixelWidth}×${pixelHeight}px = ${realWidthInches.toFixed(3)}"×${realHeightInches.toFixed(3)}" @ ${dpi} DPI`);
                }
            }

            hideFileDetails() {
                const fileDetails = document.getElementById('file-details');
                if (fileDetails) {
                    fileDetails.classList.add('hidden');
                }
            }

            getQuantityValue() {
                const quantityInput = document.getElementById('quantity-input');
                return quantityInput ? Math.max(1, parseInt(quantityInput.value) || 1) : 1;
            }

            duplicateWithQuantity() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject || !activeObject.isExactSize) {
                    this.showStatus('Please select an image first to duplicate', 'error');
                    return;
                }

                const quantity = this.getQuantityValue();
                console.log(`Duplicating selected image ${quantity} times`);

                // Get image dimensions for precise positioning
                const imageWidthInches = parseFloat(activeObject.realWidthInches);
                const imageHeightInches = parseFloat(activeObject.realHeightInches);
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const pixelsPerInch = this.displayScale;

                // Calculate how many copies fit per row
                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                let copiesCreated = 0;

                for (let i = 1; i < quantity; i++) { // Start from 1 since original exists
                    activeObject.clone((cloned) => {
                        // Calculate grid position
                        const row = Math.floor(i / copiesPerRow);
                        const col = i % copiesPerRow;

                        // Calculate precise position
                        const positionXInches = col * imageWidthInches;
                        const positionYInches = row * imageHeightInches;

                        // Check if it fits on sheet
                        if (positionXInches + imageWidthInches <= sheetWidthInches &&
                            positionYInches + imageHeightInches <= sheetHeightInches) {

                            const pixelX = positionXInches * pixelsPerInch;
                            const pixelY = positionYInches * pixelsPerInch;

                            // Snap to grid
                            const gridSpacing = this.getGridSpacing();
                            const snappedX = this.snapToGrid(pixelX, gridSpacing);
                            const snappedY = this.snapToGrid(pixelY, gridSpacing);

                            cloned.set({
                                left: snappedX,
                                top: snappedY,
                                // Preserve metadata
                                originalWidth: activeObject.originalWidth,
                                originalHeight: activeObject.originalHeight,
                                realWidthInches: activeObject.realWidthInches,
                                realHeightInches: activeObject.realHeightInches,
                                displayWidthPixels: activeObject.displayWidthPixels,
                                displayHeightPixels: activeObject.displayHeightPixels,
                                fileName: activeObject.fileName,
                                fileDPI: activeObject.fileDPI,
                                isExactSize: true,
                                isDuplicate: true
                            });

                            this.fabricCanvas.add(cloned);
                            copiesCreated++;

                            console.log(`Copy ${i} placed at (${positionXInches.toFixed(3)}", ${positionYInches.toFixed(3)})`);
                        }
                    });
                }

                this.fabricCanvas.renderAll();
                this.updateStats();
                this.showStatus(`Created ${copiesCreated + 1} copies (${quantity} total) with precise positioning`, 'success');
            }

            fillEntireSheet() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject || !activeObject.isExactSize) {
                    this.showStatus('Please select an image first to fill entire sheet', 'error');
                    return;
                }

                // Calculate maximum copies that fit
                const imageWidthInches = parseFloat(activeObject.realWidthInches);
                const imageHeightInches = parseFloat(activeObject.realHeightInches);
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();

                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                const rowsPerSheet = Math.floor(sheetHeightInches / imageHeightInches);
                const maxCopies = copiesPerRow * rowsPerSheet;

                console.log(`ENTIRE SHEET calculation:`);
                console.log(`Sheet: ${sheetWidthInches}" × ${sheetHeightInches}"`);
                console.log(`Image: ${imageWidthInches}" × ${imageHeightInches}"`);
                console.log(`Max copies: ${maxCopies} (${copiesPerRow} × ${rowsPerSheet})`);

                // Update quantity input
                const quantityInput = document.getElementById('quantity-input');
                if (quantityInput) {
                    quantityInput.value = maxCopies;
                }

                // Create all copies
                this.duplicateWithQuantity();

                const efficiency = ((copiesPerRow * imageWidthInches * rowsPerSheet * imageHeightInches) / (sheetWidthInches * sheetHeightInches) * 100).toFixed(1);
                this.showStatus(`ENTIRE SHEET: ${maxCopies} copies (${copiesPerRow}×${rowsPerSheet}) = ${efficiency}% efficiency`, 'success');
            }

            showLeftMenuQuantitySelector() {
                const quantitySection = document.getElementById('quantity-nav-section');
                if (quantitySection) {
                    quantitySection.style.display = 'block';
                    console.log('LEFT MENU quantity selector shown');
                }
            }

            hideLeftMenuQuantitySelector() {
                const quantitySection = document.getElementById('quantity-nav-section');
                if (quantitySection) {
                    quantitySection.style.display = 'none';
                    console.log('LEFT MENU quantity selector hidden');
                }
            }

            getLeftMenuQuantityValue() {
                const quantityInput = document.getElementById('sidebar-quantity-input') || document.getElementById('left-menu-quantity');
                return quantityInput ? Math.max(1, parseInt(quantityInput.value) || 1) : 1;
            }

            leftMenuDuplicateWithQuantity() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject || !activeObject.isExactSize) {
                    this.showStatus('Please select an image first to duplicate from LEFT MENU', 'error');
                    return;
                }

                const quantity = this.getLeftMenuQuantityValue();
                console.log(`LEFT MENU: Duplicating selected image ${quantity} times`);

                // Get image dimensions for precise positioning
                const imageWidthInches = parseFloat(activeObject.realWidthInches);
                const imageHeightInches = parseFloat(activeObject.realHeightInches);
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const pixelsPerInch = this.displayScale;

                // Calculate how many copies fit per row
                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                let copiesCreated = 0;

                for (let i = 1; i < quantity; i++) { // Start from 1 since original exists
                    activeObject.clone((cloned) => {
                        // Calculate grid position
                        const row = Math.floor(i / copiesPerRow);
                        const col = i % copiesPerRow;

                        // Calculate precise position
                        const positionXInches = col * imageWidthInches;
                        const positionYInches = row * imageHeightInches;

                        // Check if it fits on sheet
                        if (positionXInches + imageWidthInches <= sheetWidthInches &&
                            positionYInches + imageHeightInches <= sheetHeightInches) {

                            const pixelX = positionXInches * pixelsPerInch;
                            const pixelY = positionYInches * pixelsPerInch;

                            // Snap to grid
                            const gridSpacing = this.getGridSpacing();
                            const snappedX = this.snapToGrid(pixelX, gridSpacing);
                            const snappedY = this.snapToGrid(pixelY, gridSpacing);

                            cloned.set({
                                left: snappedX,
                                top: snappedY,
                                // Preserve metadata
                                originalWidth: activeObject.originalWidth,
                                originalHeight: activeObject.originalHeight,
                                realWidthInches: activeObject.realWidthInches,
                                realHeightInches: activeObject.realHeightInches,
                                displayWidthPixels: activeObject.displayWidthPixels,
                                displayHeightPixels: activeObject.displayHeightPixels,
                                fileName: activeObject.fileName,
                                fileDPI: activeObject.fileDPI,
                                isExactSize: true,
                                isDuplicate: true,
                                isLeftMenuDuplicate: true
                            });

                            this.fabricCanvas.add(cloned);
                            copiesCreated++;

                            console.log(`LEFT MENU Copy ${i} placed at (${positionXInches.toFixed(3)}", ${positionYInches.toFixed(3)})`);
                        }
                    });
                }

                this.fabricCanvas.renderAll();
                this.updateStats();
                this.showStatus(`LEFT MENU: Created ${copiesCreated + 1} copies (${quantity} total) with precise positioning`, 'success');
            }

            leftMenuFillEntireSheet() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject || !activeObject.isExactSize) {
                    this.showStatus('Please select an image first to fill entire sheet from LEFT MENU', 'error');
                    return;
                }

                // Calculate maximum copies that fit
                const imageWidthInches = parseFloat(activeObject.realWidthInches);
                const imageHeightInches = parseFloat(activeObject.realHeightInches);
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();

                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                const rowsPerSheet = Math.floor(sheetHeightInches / imageHeightInches);
                const maxCopies = copiesPerRow * rowsPerSheet;

                console.log(`LEFT MENU ENTIRE SHEET calculation:`);
                console.log(`Sheet: ${sheetWidthInches}" × ${sheetHeightInches}"`);
                console.log(`Image: ${imageWidthInches}" × ${imageHeightInches}"`);
                console.log(`Max copies: ${maxCopies} (${copiesPerRow} × ${rowsPerSheet})`);

                // Update quantity inputs in LEFT MENU
                const quantityInput1 = document.getElementById('sidebar-quantity-input');
                const quantityInput2 = document.getElementById('left-menu-quantity');
                if (quantityInput1) quantityInput1.value = maxCopies;
                if (quantityInput2) quantityInput2.value = maxCopies;

                // Create all copies
                this.leftMenuDuplicateWithQuantity();

                const efficiency = ((copiesPerRow * imageWidthInches * rowsPerSheet * imageHeightInches) / (sheetWidthInches * sheetHeightInches) * 100).toFixed(1);
                this.showStatus(`LEFT MENU ENTIRE SHEET: ${maxCopies} copies (${copiesPerRow}×${rowsPerSheet}) = ${efficiency}% efficiency`, 'success');
            }

            autoFillImageQuantity(index) {
                const imageData = this.uploadedImages[index];
                if (!imageData) return;

                // Calculate maximum copies that fit
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const imageWidthInches = parseFloat(imageData.realWidthInches);
                const imageHeightInches = parseFloat(imageData.realHeightInches);

                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                const rowsPerSheet = Math.floor(sheetHeightInches / imageHeightInches);
                const maxCopies = copiesPerRow * rowsPerSheet;

                if (maxCopies > 0) {
                    // Update the quantity input field
                    const quantityInput = document.querySelector(`input[onchange*="updateImageQuantity(${index}"]`);
                    if (quantityInput) {
                        quantityInput.value = maxCopies;
                        this.updateImageQuantity(index, maxCopies);
                    }

                    this.showStatus(`Auto-filled: ${maxCopies} copies (${copiesPerRow}×${rowsPerSheet}) for maximum efficiency`, 'success');
                } else {
                    this.showStatus('Image is too large to fit multiple copies on this sheet', 'error');
                }
            }

            createAdditionalCopies(index, additionalCopies) {
                if (!this.uploadedImages[index] || !this.uploadedImages[index].fabricObject) return;

                const originalObj = this.uploadedImages[index].fabricObject;
                const imageData = this.uploadedImages[index];

                console.log(`Creating ${additionalCopies} additional copies of ${imageData.name}`);

                // Get sheet and image dimensions for precise grid layout
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const imageWidthInches = parseFloat(imageData.realWidthInches);
                const imageHeightInches = parseFloat(imageData.realHeightInches);
                const pixelsPerInch = this.displayScale;

                // Calculate how many copies fit per row (tight packing)
                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);

                console.log(`Grid layout: ${copiesPerRow} copies per row, image size: ${imageWidthInches}" × ${imageHeightInches}"`);

                for (let i = 0; i < additionalCopies; i++) {
                    originalObj.clone((cloned) => {
                        const copyNumber = i + 1; // Start from 1 (original is 0)

                        // Calculate grid position (row and column)
                        const row = Math.floor(copyNumber / copiesPerRow);
                        const col = copyNumber % copiesPerRow;

                        // Calculate EXACT pixel positions on grid
                        const gridX = col * imageWidthInches * pixelsPerInch;
                        const gridY = row * imageHeightInches * pixelsPerInch;

                        // Snap to grid for perfect alignment
                        const gridSpacing = this.getGridSpacing();
                        const snappedX = this.snapToGrid(gridX, gridSpacing);
                        const snappedY = this.snapToGrid(gridY, gridSpacing);

                        console.log(`Copy ${copyNumber}: Row ${row}, Col ${col} → Position (${(snappedX/pixelsPerInch).toFixed(3)}", ${(snappedY/pixelsPerInch).toFixed(3)}")`);

                        cloned.set({
                            left: snappedX,
                            top: snappedY,
                            // Preserve all exact size metadata
                            originalWidth: originalObj.originalWidth,
                            originalHeight: originalObj.originalHeight,
                            realWidthInches: originalObj.realWidthInches,
                            realHeightInches: originalObj.realHeightInches,
                            displayWidthPixels: originalObj.displayWidthPixels,
                            displayHeightPixels: originalObj.displayHeightPixels,
                            fileName: originalObj.fileName,
                            fileDPI: originalObj.fileDPI,
                            isExactSize: true,
                            isQuantityCopy: true,
                            parentImageIndex: index
                        });

                        // Add to canvas
                        this.fabricCanvas.add(cloned);
                        this.fabricCanvas.renderAll();
                    });
                }

                this.showStatus(`Created ${additionalCopies} copies in precise grid layout`, 'success');
            }

            createQuantityCopies(index, numberOfCopies) {
                if (!this.uploadedImages[index] || !this.uploadedImages[index].fabricObject) return;

                const originalObj = this.uploadedImages[index].fabricObject;
                const imageData = this.uploadedImages[index];

                console.log(`Creating ${numberOfCopies} quantity copies of ${imageData.name}`);

                // Get PRECISE sheet and image dimensions
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const imageWidthInches = parseFloat(imageData.realWidthInches);
                const imageHeightInches = parseFloat(imageData.realHeightInches);
                const pixelsPerInch = this.displayScale;

                // Calculate maximum copies that can fit on sheet
                const maxCopiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                const maxRows = Math.floor(sheetHeightInches / imageHeightInches);
                const maxPossibleCopies = maxCopiesPerRow * maxRows;

                // Limit to what actually fits on the sheet
                const actualCopies = Math.min(numberOfCopies, maxPossibleCopies - 1); // -1 because original counts as 1

                console.log(`Sheet: ${sheetWidthInches}" × ${sheetHeightInches}"`);
                console.log(`Image: ${imageWidthInches}" × ${imageHeightInches}"`);
                console.log(`Max possible: ${maxPossibleCopies} (${maxCopiesPerRow} × ${maxRows})`);
                console.log(`Creating: ${actualCopies} copies (requested: ${numberOfCopies})`);

                let copiesCreated = 0;

                for (let i = 0; i < actualCopies; i++) {
                    originalObj.clone((cloned) => {
                        const copyNumber = i + 1; // Start from 1 (original is 0)

                        // Calculate grid position (row and column)
                        const row = Math.floor(copyNumber / maxCopiesPerRow);
                        const col = copyNumber % maxCopiesPerRow;

                        // Calculate PRECISE positions in inches, then convert to pixels
                        const positionXInches = col * imageWidthInches;
                        const positionYInches = row * imageHeightInches;

                        // Check if this position fits within sheet boundaries
                        if (positionXInches + imageWidthInches <= sheetWidthInches &&
                            positionYInches + imageHeightInches <= sheetHeightInches) {

                            // Convert to pixels and snap to grid
                            const gridX = positionXInches * pixelsPerInch;
                            const gridY = positionYInches * pixelsPerInch;

                            const gridSpacing = this.getGridSpacing();
                            const snappedX = this.snapToGrid(gridX, gridSpacing);
                            const snappedY = this.snapToGrid(gridY, gridSpacing);

                            console.log(`Copy ${copyNumber}: (${positionXInches.toFixed(3)}", ${positionYInches.toFixed(3)}") → Pixels (${snappedX}, ${snappedY})`);

                            cloned.set({
                                left: snappedX,
                                top: snappedY,
                                // Preserve all exact size metadata
                                originalWidth: originalObj.originalWidth,
                                originalHeight: originalObj.originalHeight,
                                realWidthInches: originalObj.realWidthInches,
                                realHeightInches: originalObj.realHeightInches,
                                displayWidthPixels: originalObj.displayWidthPixels,
                                displayHeightPixels: originalObj.displayHeightPixels,
                                fileName: originalObj.fileName,
                                fileDPI: originalObj.fileDPI,
                                isExactSize: true,
                                isQuantityCopy: true,
                                parentImageIndex: index
                            });

                            // Add to canvas
                            this.fabricCanvas.add(cloned);
                            copiesCreated++;
                        }
                    });
                }

                this.fabricCanvas.renderAll();

                if (copiesCreated < numberOfCopies) {
                    this.showStatus(`Created ${copiesCreated + 1} copies (${numberOfCopies - copiesCreated} didn't fit on ${sheetWidthInches}" × ${sheetHeightInches}" sheet)`, 'warning');
                } else {
                    this.showStatus(`Created ${copiesCreated + 1} copies in precise grid layout`, 'success');
                }
            }

            removeAllQuantityCopies(index) {
                // Find and remove ALL quantity copies for this image
                const objectsToRemove = [];

                this.fabricCanvas.forEachObject((obj) => {
                    if (obj.isQuantityCopy && obj.parentImageIndex === index) {
                        objectsToRemove.push(obj);
                    }
                });

                objectsToRemove.forEach(obj => {
                    this.fabricCanvas.remove(obj);
                });

                this.fabricCanvas.renderAll();
                console.log(`Removed ${objectsToRemove.length} quantity copies for image ${index}`);
            }

            removeExcessCopies(index, excessCopies) {
                // Find and remove excess copies from canvas
                const objectsToRemove = [];
                let removedCount = 0;

                this.fabricCanvas.forEachObject((obj) => {
                    if (obj.isQuantityCopy && obj.parentImageIndex === index && removedCount < excessCopies) {
                        objectsToRemove.push(obj);
                        removedCount++;
                    }
                });

                objectsToRemove.forEach(obj => {
                    this.fabricCanvas.remove(obj);
                });

                this.fabricCanvas.renderAll();
                console.log(`Removed ${removedCount} excess copies`);
                this.showStatus(`Removed ${removedCount} excess copies`, 'info');
            }

            showQuantitySelector(imageIndex) {
                console.log('showQuantitySelector called for index:', imageIndex);
                alert('showQuantitySelector called for index: ' + imageIndex); // DEBUG ALERT

                const imageData = this.uploadedImages[imageIndex];
                if (!imageData) {
                    console.error('No image data found for index:', imageIndex);
                    alert('ERROR: No image data found for index: ' + imageIndex);
                    return;
                }
                console.log('Showing quantity selector for:', imageData.name);
                alert('About to create popup for: ' + imageData.name); // DEBUG ALERT

                // Remove any existing popups first
                this.closeQuantityPopup();

                // Create quantity selector popup
                const popup = document.createElement('div');
                popup.id = 'quantity-popup';
                popup.style.cssText = `
                    position: fixed !important;
                    top: 50% !important;
                    left: 50% !important;
                    transform: translate(-50%, -50%) !important;
                    background: white !important;
                    border: 3px solid #e74c3c !important;
                    border-radius: 10px !important;
                    padding: 20px !important;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.8) !important;
                    z-index: 999999 !important;
                    min-width: 350px !important;
                    text-align: center !important;
                    display: block !important;
                    visibility: visible !important;
                `;

                popup.innerHTML = `
                    <h3 style="margin: 0 0 15px 0; color: #2c3e50;">📏 Set Quantity for Image</h3>
                    <div style="margin-bottom: 15px;">
                        <strong>${imageData.name}</strong><br>
                        <span style="color: #e74c3c; font-size: 1.1rem; font-weight: 600;">${imageData.realWidthInches}" × ${imageData.realHeightInches}"</span>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600;">How many copies?</label>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                            <button onclick="window.dtfBuilder.adjustQuantityPopup(-1)" style="background: #e74c3c; color: white; border: none; border-radius: 5px; width: 35px; height: 35px; font-size: 20px; cursor: pointer; font-weight: bold;">-</button>
                            <input type="number" id="popup-quantity" value="1" min="1" max="999" style="width: 80px; text-align: center; font-size: 20px; padding: 8px; border: 3px solid #3498db; border-radius: 5px; font-weight: bold;">
                            <button onclick="window.dtfBuilder.adjustQuantityPopup(1)" style="background: #27ae60; color: white; border: none; border-radius: 5px; width: 35px; height: 35px; font-size: 20px; cursor: pointer; font-weight: bold;">+</button>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px; text-align: center;">
                        <button onclick="window.dtfBuilder.autoFillSheet(${imageIndex})" style="background: #9b59b6; color: white; border: none; padding: 15px 25px; border-radius: 8px; cursor: pointer; font-weight: 700; font-size: 16px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); transition: all 0.2s;">
                            🔄 AUTO-FILL ENTIRE SHEET
                        </button>
                        <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                            Calculates maximum copies that fit on sheet
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="window.dtfBuilder.applyQuantityPopup(${imageIndex})" style="background: #3498db; color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; font-weight: 600; font-size: 14px;">
                            ✓ Apply Quantity
                        </button>
                        <button onclick="window.dtfBuilder.closeQuantityPopup()" style="background: #95a5a6; color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; font-weight: 600; font-size: 14px;">
                            ✕ Cancel
                        </button>
                    </div>
                `;

                // Add overlay
                const overlay = document.createElement('div');
                overlay.id = 'quantity-overlay';
                overlay.style.cssText = `
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background: rgba(0,0,0,0.8) !important;
                    z-index: 999998 !important;
                    display: block !important;
                    visibility: visible !important;
                `;

                document.body.appendChild(overlay);
                document.body.appendChild(popup);

                console.log('Quantity selector popup created and added to DOM');
                console.log('Popup element:', popup);
                console.log('Overlay element:', overlay);

                // Focus on quantity input
                const quantityInput = document.getElementById('popup-quantity');
                quantityInput.focus();
                quantityInput.select();

                // Add keyboard support
                quantityInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        this.applyQuantityPopup(imageIndex);
                    } else if (e.key === 'Escape') {
                        this.closeQuantityPopup();
                    }
                });

                // Close on overlay click
                overlay.addEventListener('click', () => {
                    this.closeQuantityPopup();
                });
            }

            adjustQuantityPopup(change) {
                const input = document.getElementById('popup-quantity');
                if (input) {
                    const newValue = Math.max(1, Math.min(100, parseInt(input.value) + change));
                    input.value = newValue;
                }
            }

            applyQuantityPopup(imageIndex) {
                const input = document.getElementById('popup-quantity');
                if (input) {
                    const quantity = parseInt(input.value);
                    this.updateImageQuantity(imageIndex, quantity);
                    this.closeQuantityPopup();
                }
            }

            closeQuantityPopup() {
                const popup = document.getElementById('quantity-popup');
                const overlay = document.getElementById('quantity-overlay');
                if (popup) popup.remove();
                if (overlay) overlay.remove();
            }

            autoFillSheet(imageIndex) {
                console.log('AUTO-FILL ENTIRE SHEET clicked for index:', imageIndex);
                alert('AUTO-FILL ENTIRE SHEET clicked!'); // DEBUG ALERT

                const imageData = this.uploadedImages[imageIndex];
                if (!imageData) {
                    alert('ERROR: No image data found for auto-fill');
                    return;
                }

                // Get PRECISE sheet dimensions and image dimensions
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const imageWidthInches = parseFloat(imageData.realWidthInches);
                const imageHeightInches = parseFloat(imageData.realHeightInches);

                console.log(`PRECISE AUTO-FILL CALCULATION:`);
                console.log(`Sheet: ${sheetWidthInches}" × ${sheetHeightInches}" (${(sheetWidthInches * sheetHeightInches).toFixed(1)} sq in)`);
                console.log(`Image: ${imageWidthInches}" × ${imageHeightInches}" (${(imageWidthInches * imageHeightInches).toFixed(3)} sq in)`);

                // Calculate EXACT copies that fit (tight packing for DTF printing)
                const copiesPerRow = Math.floor(sheetWidthInches / imageWidthInches);
                const rowsPerSheet = Math.floor(sheetHeightInches / imageHeightInches);
                const totalCopies = copiesPerRow * rowsPerSheet;

                // Calculate PRECISE measurements for print verification
                const usedWidthInches = copiesPerRow * imageWidthInches;
                const usedHeightInches = rowsPerSheet * imageHeightInches;
                const wastedWidthInches = sheetWidthInches - usedWidthInches;
                const wastedHeightInches = sheetHeightInches - usedHeightInches;
                const usedAreaSqIn = usedWidthInches * usedHeightInches;
                const totalAreaSqIn = sheetWidthInches * sheetHeightInches;
                const efficiency = (usedAreaSqIn / totalAreaSqIn * 100).toFixed(1);

                console.log(`LAYOUT ANALYSIS:`);
                console.log(`- Copies per row: ${copiesPerRow} (uses ${usedWidthInches}" of ${sheetWidthInches}", wastes ${wastedWidthInches.toFixed(3)}")`);
                console.log(`- Rows per sheet: ${rowsPerSheet} (uses ${usedHeightInches}" of ${sheetHeightInches}", wastes ${wastedHeightInches.toFixed(3)}")`);
                console.log(`- Total copies: ${totalCopies}`);
                console.log(`- Used area: ${usedAreaSqIn.toFixed(1)} sq in of ${totalAreaSqIn} sq in`);
                console.log(`- Efficiency: ${efficiency}%`);
                console.log(`- Waste: ${wastedWidthInches.toFixed(3)}" × ${wastedHeightInches.toFixed(3)}" = ${(wastedWidthInches * wastedHeightInches).toFixed(3)} sq in`);

                if (totalCopies > 0) {
                    // Update the popup quantity
                    const input = document.getElementById('popup-quantity');
                    if (input) {
                        input.value = totalCopies;
                    }

                    this.showStatus(`PRECISE: ${totalCopies} copies (${copiesPerRow}×${rowsPerSheet}) = ${efficiency}% efficiency, waste: ${wastedWidthInches.toFixed(3)}"×${wastedHeightInches.toFixed(3)}"`, 'info');
                } else {
                    this.showStatus(`ERROR: Image ${imageWidthInches}"×${imageHeightInches}" is too large for ${sheetWidthInches}"×${sheetHeightInches}" sheet`, 'error');
                }
            }

            autoFillSelectedImage() {
                // Get the currently selected object
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject || !activeObject.isExactSize) {
                    this.showStatus('Please select an image first to auto-fill the sheet', 'error');
                    return;
                }

                // Find the image in our uploaded images list
                const imageIndex = this.uploadedImages.findIndex(img => img.fabricObject === activeObject);
                if (imageIndex === -1) {
                    this.showStatus('Selected object not found in image list', 'error');
                    return;
                }

                // Show the quantity selector with auto-fill option
                this.showQuantitySelector(imageIndex);
            }

            selectImageOnCanvas(index) {
                if (this.uploadedImages[index] && this.uploadedImages[index].fabricObject) {
                    const fabricObj = this.uploadedImages[index].fabricObject;
                    this.fabricCanvas.setActiveObject(fabricObj);
                    this.fabricCanvas.renderAll();

                    // Center the view on the selected object
                    const objCenter = fabricObj.getCenterPoint();
                    this.fabricCanvas.viewportCenterObject(fabricObj);

                    this.showStatus(`Selected: ${this.uploadedImages[index].name}`, 'info');
                    console.log('Selected image on canvas:', this.uploadedImages[index].name);
                }
            }

            duplicateImageOnCanvas(index) {
                if (this.uploadedImages[index] && this.uploadedImages[index].fabricObject) {
                    const originalObj = this.uploadedImages[index].fabricObject;
                    const imageData = this.uploadedImages[index];

                    console.log('Duplicating image:', imageData.name);

                    // Clone the fabric object
                    originalObj.clone((cloned) => {
                        // Calculate precise grid positioning
                        const imageWidthInches = parseFloat(imageData.realWidthInches);
                        const pixelsPerInch = this.displayScale;

                        // Position duplicate exactly one image width to the right (tight packing)
                        const offsetX = imageWidthInches * pixelsPerInch;
                        const offsetY = 0; // Keep same vertical position

                        // Snap to grid for perfect alignment
                        const gridSpacing = this.getGridSpacing();
                        const newLeft = this.snapToGrid(originalObj.left + offsetX, gridSpacing);
                        const newTop = this.snapToGrid(originalObj.top + offsetY, gridSpacing);

                        console.log(`Duplicate positioned at: (${(newLeft/pixelsPerInch).toFixed(3)}", ${(newTop/pixelsPerInch).toFixed(3)}")`);

                        cloned.set({
                            left: newLeft,
                            top: newTop,
                            // Preserve all exact size metadata
                            originalWidth: originalObj.originalWidth,
                            originalHeight: originalObj.originalHeight,
                            realWidthInches: originalObj.realWidthInches,
                            realHeightInches: originalObj.realHeightInches,
                            displayWidthPixels: originalObj.displayWidthPixels,
                            displayHeightPixels: originalObj.displayHeightPixels,
                            fileName: originalObj.fileName + ' (Copy)',
                            fileDPI: originalObj.fileDPI,
                            isExactSize: true
                        });

                        // Add to canvas
                        this.fabricCanvas.add(cloned);
                        this.fabricCanvas.setActiveObject(cloned);
                        this.fabricCanvas.renderAll();

                        // Add to uploaded images list
                        this.uploadedImages.push({
                            name: imageData.name + ' (Copy)',
                            width: imageData.width,
                            height: imageData.height,
                            realWidthInches: imageData.realWidthInches,
                            realHeightInches: imageData.realHeightInches,
                            displayWidthPixels: imageData.displayWidthPixels,
                            displayHeightPixels: imageData.displayHeightPixels,
                            fabricObject: cloned,
                            quantity: 1,
                            dpi: imageData.dpi
                        });

                        this.updateImageList();
                        this.updateStats();
                        this.showStatus(`Duplicated: ${imageData.name} with precise grid positioning`, 'success');
                        console.log('Image duplicated successfully with grid alignment');
                    });
                }
            }

            removeImageFromCanvas(index) {
                if (this.uploadedImages[index] && this.uploadedImages[index].fabricObject) {
                    const imageData = this.uploadedImages[index];
                    const fabricObj = imageData.fabricObject;

                    console.log('Removing image:', imageData.name);

                    // Remove from canvas
                    this.fabricCanvas.remove(fabricObj);
                    this.fabricCanvas.renderAll();

                    // Remove from uploaded images array
                    this.uploadedImages.splice(index, 1);

                    this.updateImageList();
                    this.updateStats();
                    this.showStatus(`Removed: ${imageData.name}`, 'success');
                    console.log('Image removed successfully');
                }
            }

            // Method to resize image to exact dimensions
            resizeImageToExactSize(fabricObj, widthInches, heightInches) {
                if (!fabricObj || !fabricObj.isExactSize) return;

                const displayWidthPixels = widthInches * this.displayScale;
                const displayHeightPixels = heightInches * this.displayScale;

                fabricObj.set({
                    scaleX: displayWidthPixels / fabricObj.originalWidth,
                    scaleY: displayHeightPixels / fabricObj.originalHeight
                });

                // Update metadata
                fabricObj.realWidthInches = widthInches;
                fabricObj.realHeightInches = heightInches;
                fabricObj.displayWidthPixels = displayWidthPixels;
                fabricObj.displayHeightPixels = displayHeightPixels;

                this.fabricCanvas.renderAll();
                this.updateImageList();

                console.log(`Resized image to exact: ${widthInches}" × ${heightInches}"`);
            }

            showObjectDimensions(obj) {
                if (!obj || !obj.isExactSize) return;

                const currentWidthInches = (obj.getScaledWidth() / this.displayScale).toFixed(3);
                const currentHeightInches = (obj.getScaledHeight() / this.displayScale).toFixed(3);
                const positionXInches = (obj.left / this.displayScale).toFixed(3);
                const positionYInches = (obj.top / this.displayScale).toFixed(3);

                // Update canvas status with real measurements
                document.getElementById('canvas-status').textContent =
                    `Selected: ${currentWidthInches}" × ${currentHeightInches}" at (${positionXInches}", ${positionYInches}")`;

                console.log(`Object dimensions: ${currentWidthInches}" × ${currentHeightInches}" at position (${positionXInches}", ${positionYInches}")`);
            }

            hideObjectDimensions() {
                const [sheetWidth, sheetHeight] = this.getSheetDimensions();
                document.getElementById('canvas-status').textContent = `${sheetWidth}" × ${sheetHeight}" @ ${this.dpi} DPI`;
            }

            showObjectPosition(obj) {
                if (!obj || !obj.isExactSize) return;

                const positionXInches = (obj.left / this.displayScale).toFixed(3);
                const positionYInches = (obj.top / this.displayScale).toFixed(3);

                // Show position in real-time during movement
                document.getElementById('canvas-status').textContent =
                    `Moving to: (${positionXInches}", ${positionYInches}")`;
            }

            updateObjectRealDimensions(obj) {
                if (!obj || !obj.isExactSize) return;

                // Calculate new real dimensions after scaling
                const newWidthInches = obj.getScaledWidth() / this.displayScale;
                const newHeightInches = obj.getScaledHeight() / this.displayScale;

                // Update object metadata
                obj.realWidthInches = newWidthInches;
                obj.realHeightInches = newHeightInches;
                obj.displayWidthPixels = obj.getScaledWidth();
                obj.displayHeightPixels = obj.getScaledHeight();

                // Update the image list to reflect new dimensions
                this.updateImageList();

                console.log(`Object resized to: ${newWidthInches.toFixed(3)}" × ${newHeightInches.toFixed(3)}"`);
                this.showStatus(`Resized to ${newWidthInches.toFixed(3)}" × ${newHeightInches.toFixed(3)}"`, 'info');
            }

            enableButtons() {
                document.getElementById('auto-nest-btn')?.removeAttribute('disabled');
                document.getElementById('optimize-btn')?.removeAttribute('disabled');
                document.getElementById('preview-btn')?.removeAttribute('disabled');
                document.getElementById('auto-fill-sheet-btn')?.removeAttribute('disabled');
                document.getElementById('add-to-cart-btn')?.removeAttribute('disabled');
            }

            updateStats() {
                const imageCount = this.uploadedImages.length;
                const totalCopies = this.uploadedImages.reduce((sum, img) => sum + (img.quantity || 1), 0);

                // Calculate efficiency (placeholder calculation)
                const [sheetWidth, sheetHeight] = this.getSheetDimensions();
                const sheetArea = sheetWidth * sheetHeight;
                const usedArea = this.uploadedImages.reduce((sum, img) => {
                    const copies = img.quantity || 1;
                    return sum + (parseFloat(img.realWidthInches) * parseFloat(img.realHeightInches) * copies);
                }, 0);
                const efficiency = sheetArea > 0 ? Math.min(100, (usedArea / sheetArea * 100)).toFixed(0) : 0;

                document.getElementById('image-count').textContent = imageCount;
                document.getElementById('total-copies').textContent = totalCopies;
                document.getElementById('efficiency').textContent = efficiency + '%';
            }

            showStatus(message, type = 'info') {
                const statusEl = document.getElementById('status-message');
                if (!statusEl) return;

                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
                statusEl.classList.remove('hidden');

                setTimeout(() => {
                    statusEl.classList.add('hidden');
                }, 5000);
            }

            setupToolbar() {
                // Zoom controls
                document.getElementById('zoom-in')?.addEventListener('click', () => this.zoomIn());
                document.getElementById('zoom-out')?.addEventListener('click', () => this.zoomOut());
                document.getElementById('zoom-fit')?.addEventListener('click', () => this.zoomToFit());

                // Tool buttons
                document.getElementById('select-tool')?.addEventListener('click', () => this.setTool('select'));
                document.getElementById('hand-tool')?.addEventListener('click', () => this.setTool('hand'));
                document.getElementById('move-tool')?.addEventListener('click', () => this.setTool('move'));
                document.getElementById('rotate-tool')?.addEventListener('click', () => this.rotateSelected());
                document.getElementById('duplicate-tool')?.addEventListener('click', () => this.duplicateSelected());
                document.getElementById('delete-tool')?.addEventListener('click', () => this.deleteSelected());
                document.getElementById('grid-toggle')?.addEventListener('click', () => this.toggleGrid());
                document.getElementById('reset-view')?.addEventListener('click', () => this.resetCanvasView());

                // Action buttons
                document.getElementById('auto-nest-btn')?.addEventListener('click', () => this.autoNest());
                document.getElementById('optimize-btn')?.addEventListener('click', () => this.optimizeLayout());
                document.getElementById('preview-btn')?.addEventListener('click', () => this.previewPrint());
                document.getElementById('auto-fill-sheet-btn')?.addEventListener('click', () => this.autoFillSelectedImage());
                document.getElementById('download-btn')?.addEventListener('click', () => this.downloadGangSheet());
                document.getElementById('save-project-btn')?.addEventListener('click', () => this.saveProject());

                // Quantity selector buttons (old ones)
                document.getElementById('duplicate-btn')?.addEventListener('click', () => this.duplicateWithQuantity());
                document.getElementById('entire-sheet-btn')?.addEventListener('click', () => this.fillEntireSheet());

                // LEFT MENU quantity selector buttons
                document.getElementById('sidebar-duplicate-btn')?.addEventListener('click', () => this.leftMenuDuplicateWithQuantity());
                document.getElementById('sidebar-entire-sheet-btn')?.addEventListener('click', () => this.leftMenuFillEntireSheet());
                document.getElementById('left-menu-duplicate')?.addEventListener('click', () => this.leftMenuDuplicateWithQuantity());
                document.getElementById('left-menu-entire-sheet')?.addEventListener('click', () => this.leftMenuFillEntireSheet());
            }

            // Toolbar methods
            zoomIn() {
                this.zoomLevel = Math.min(this.zoomLevel * 1.2, 5);
                this.fabricCanvas.setZoom(this.zoomLevel);
                this.updateZoomDisplay();
            }

            zoomOut() {
                this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.1);
                this.fabricCanvas.setZoom(this.zoomLevel);
                this.updateZoomDisplay();
            }

            zoomToFit() {
                this.zoomLevel = 1;
                this.fabricCanvas.setZoom(1);
                this.fabricCanvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
                this.updateZoomDisplay();
            }

            updateZoomDisplay() {
                const zoomPercent = Math.round(this.zoomLevel * 100);
                document.getElementById('zoom-level').textContent = zoomPercent + '%';
            }

            setTool(tool) {
                this.currentTool = tool;
                console.log('Tool changed to:', tool);

                // Update tool button states
                document.querySelectorAll('.tool-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById(tool + '-tool')?.classList.add('active');

                // Update canvas interaction mode
                if (tool === 'select') {
                    this.fabricCanvas.selection = true;
                    this.fabricCanvas.defaultCursor = 'default';
                    this.fabricCanvas.hoverCursor = 'move';
                    this.disablePanning();
                } else if (tool === 'move') {
                    this.fabricCanvas.selection = false;
                    this.fabricCanvas.defaultCursor = 'move';
                    this.disablePanning();
                } else if (tool === 'hand') {
                    this.fabricCanvas.selection = false;
                    this.fabricCanvas.defaultCursor = 'grab';
                    this.fabricCanvas.hoverCursor = 'grab';
                    this.enablePanning();
                }

                // Update canvas container class for styling
                const container = document.querySelector('.canvas-container');
                if (tool === 'hand') {
                    container?.classList.add('panning');
                } else {
                    container?.classList.remove('panning');
                }
            }

            enablePanning() {
                if (!this.fabricCanvas) return;

                console.log('Enabling canvas panning with boundary constraints');

                // Remove existing pan event listeners to avoid duplicates
                this.disablePanning();

                this.isPanning = false;
                this.lastPanPoint = null;

                // Get sheet boundaries for constraint calculations
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const sheetWidthPixels = sheetWidthInches * this.displayScale;
                const sheetHeightPixels = sheetHeightInches * this.displayScale;

                console.log(`Sheet boundaries: ${sheetWidthPixels} × ${sheetHeightPixels} pixels`);

                // Mouse events for panning
                this.panMouseDown = (opt) => {
                    const evt = opt.e;
                    if (this.currentTool === 'hand') {
                        this.isPanning = true;
                        this.fabricCanvas.selection = false;
                        this.lastPanPoint = { x: evt.clientX, y: evt.clientY };
                        this.fabricCanvas.defaultCursor = 'grabbing';
                        console.log('Pan started');
                    }
                };

                this.panMouseMove = (opt) => {
                    if (this.isPanning && this.lastPanPoint) {
                        const evt = opt.e;
                        const vpt = this.fabricCanvas.viewportTransform;
                        const zoom = vpt[0]; // Current zoom level

                        // Calculate new pan position
                        const deltaX = evt.clientX - this.lastPanPoint.x;
                        const deltaY = evt.clientY - this.lastPanPoint.y;

                        let newX = vpt[4] + deltaX;
                        let newY = vpt[5] + deltaY;

                        // Apply boundary constraints
                        const canvasWidth = this.fabricCanvas.getWidth();
                        const canvasHeight = this.fabricCanvas.getHeight();

                        // Calculate the visible sheet area in current zoom
                        const visibleSheetWidth = sheetWidthPixels * zoom;
                        const visibleSheetHeight = sheetHeightPixels * zoom;

                        // Constrain panning to keep sheet content visible
                        const minX = canvasWidth - visibleSheetWidth;
                        const maxX = 0;
                        const minY = canvasHeight - visibleSheetHeight;
                        const maxY = 0;

                        newX = Math.max(minX, Math.min(maxX, newX));
                        newY = Math.max(minY, Math.min(maxY, newY));

                        // Apply constrained transform
                        vpt[4] = newX;
                        vpt[5] = newY;

                        this.fabricCanvas.requestRenderAll();
                        this.lastPanPoint = { x: evt.clientX, y: evt.clientY };

                        // Log current view position
                        const viewX = (-newX / zoom / this.displayScale).toFixed(2);
                        const viewY = (-newY / zoom / this.displayScale).toFixed(2);
                        console.log(`View position: (${viewX}", ${viewY}") at ${(zoom * 100).toFixed(0)}% zoom`);
                    }
                };

                this.panMouseUp = () => {
                    if (this.isPanning) {
                        this.isPanning = false;
                        this.fabricCanvas.defaultCursor = 'grab';
                        this.fabricCanvas.selection = true;
                        console.log('Pan ended');
                    }
                };

                // Add event listeners
                this.fabricCanvas.on('mouse:down', this.panMouseDown);
                this.fabricCanvas.on('mouse:move', this.panMouseMove);
                this.fabricCanvas.on('mouse:up', this.panMouseUp);

                // Disable object selection while panning
                this.fabricCanvas.forEachObject((obj) => {
                    obj.selectable = false;
                    obj.evented = false;
                });

                this.fabricCanvas.renderAll();
            }

            disablePanning() {
                if (!this.fabricCanvas) return;

                console.log('Disabling canvas panning');

                // Remove pan event listeners
                if (this.panMouseDown) this.fabricCanvas.off('mouse:down', this.panMouseDown);
                if (this.panMouseMove) this.fabricCanvas.off('mouse:move', this.panMouseMove);
                if (this.panMouseUp) this.fabricCanvas.off('mouse:up', this.panMouseUp);

                this.isPanning = false;
                this.lastPanPoint = null;

                // Re-enable object selection
                this.fabricCanvas.forEachObject((obj) => {
                    if (!obj.isGrid) { // Don't make grid objects selectable
                        obj.selectable = true;
                        obj.evented = true;
                    }
                });

                this.fabricCanvas.renderAll();
            }

            // Reset canvas view to show full sheet
            resetCanvasView() {
                if (!this.fabricCanvas) return;

                // Get sheet and canvas dimensions
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const sheetWidthPixels = sheetWidthInches * this.displayScale;
                const sheetHeightPixels = sheetHeightInches * this.displayScale;
                const canvasWidth = this.fabricCanvas.getWidth();
                const canvasHeight = this.fabricCanvas.getHeight();

                // Calculate zoom to fit sheet in canvas with some padding
                const zoomX = (canvasWidth * 0.9) / sheetWidthPixels;
                const zoomY = (canvasHeight * 0.9) / sheetHeightPixels;
                const zoom = Math.min(zoomX, zoomY, 1); // Don't zoom in beyond 100%

                // Center the sheet in the canvas
                const offsetX = (canvasWidth - sheetWidthPixels * zoom) / 2;
                const offsetY = (canvasHeight - sheetHeightPixels * zoom) / 2;

                // Apply the transform
                this.fabricCanvas.setViewportTransform([zoom, 0, 0, zoom, offsetX, offsetY]);
                this.fabricCanvas.renderAll();

                // Update zoom display
                this.zoomLevel = zoom;
                this.updateZoomDisplay();

                this.showStatus(`View reset: ${(zoom * 100).toFixed(0)}% zoom, sheet centered`, 'info');
                console.log(`Canvas view reset: ${(zoom * 100).toFixed(0)}% zoom, sheet centered`);
            }

            enableMouseWheelZoom() {
                if (!this.fabricCanvas) return;

                this.fabricCanvas.on('mouse:wheel', (opt) => {
                    const delta = opt.e.deltaY;
                    let zoom = this.fabricCanvas.getZoom();
                    zoom *= 0.999 ** delta;

                    // Limit zoom range for practical use
                    if (zoom > 5) zoom = 5;   // Max 500% zoom
                    if (zoom < 0.1) zoom = 0.1; // Min 10% zoom

                    // Zoom towards mouse position
                    this.fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);

                    // Apply boundary constraints after zoom
                    this.constrainViewport();

                    // Update zoom display
                    this.zoomLevel = zoom;
                    this.updateZoomDisplay();

                    opt.e.preventDefault();
                    opt.e.stopPropagation();

                    console.log(`Zoom: ${(zoom * 100).toFixed(0)}%`);
                });

                console.log('Mouse wheel zoom enabled with boundary constraints');
            }

            constrainViewport() {
                if (!this.fabricCanvas) return;

                const vpt = this.fabricCanvas.viewportTransform;
                const zoom = vpt[0];

                // Get sheet boundaries
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                const sheetWidthPixels = sheetWidthInches * this.displayScale;
                const sheetHeightPixels = sheetHeightInches * this.displayScale;

                // Calculate canvas dimensions
                const canvasWidth = this.fabricCanvas.getWidth();
                const canvasHeight = this.fabricCanvas.getHeight();

                // Calculate the visible sheet area in current zoom
                const visibleSheetWidth = sheetWidthPixels * zoom;
                const visibleSheetHeight = sheetHeightPixels * zoom;

                // Constrain viewport to keep sheet content visible
                const minX = canvasWidth - visibleSheetWidth;
                const maxX = 0;
                const minY = canvasHeight - visibleSheetHeight;
                const maxY = 0;

                let newX = Math.max(minX, Math.min(maxX, vpt[4]));
                let newY = Math.max(minY, Math.min(maxY, vpt[5]));

                // Apply constraints
                vpt[4] = newX;
                vpt[5] = newY;

                this.fabricCanvas.requestRenderAll();
            }

            rotateSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject) {
                    activeObject.rotate(activeObject.angle + 90);
                    this.fabricCanvas.renderAll();
                    this.showStatus('Object rotated 90 degrees', 'success');
                }
            }

            duplicateSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    console.log('Duplicating selected object');

                    activeObject.clone((cloned) => {
                        // Calculate smart spacing based on object size
                        let offsetX, offsetY;

                        if (activeObject.isExactSize) {
                            // For images, use image width + spacing
                            const imageWidthInches = parseFloat(activeObject.realWidthInches);
                            const spacingInches = 0.25;
                            offsetX = (imageWidthInches + spacingInches) * this.displayScale;
                            offsetY = 0; // Keep same vertical position
                        } else {
                            // For other objects, use object dimensions + spacing
                            const objectWidth = activeObject.getScaledWidth();
                            const spacingPixels = this.displayScale * 0.25; // 0.25 inch spacing
                            offsetX = objectWidth + spacingPixels;
                            offsetY = 0;
                        }

                        cloned.set({
                            left: activeObject.left + offsetX,
                            top: activeObject.top + offsetY
                        });

                        // If it's an exact size image, preserve metadata
                        if (activeObject.isExactSize) {
                            cloned.set({
                                originalWidth: activeObject.originalWidth,
                                originalHeight: activeObject.originalHeight,
                                realWidthInches: activeObject.realWidthInches,
                                realHeightInches: activeObject.realHeightInches,
                                displayWidthPixels: activeObject.displayWidthPixels,
                                displayHeightPixels: activeObject.displayHeightPixels,
                                fileName: (activeObject.fileName || 'Object') + ' (Copy)',
                                fileDPI: activeObject.fileDPI,
                                isExactSize: true
                            });

                            // Add to uploaded images list if it's an image
                            this.uploadedImages.push({
                                name: (activeObject.fileName || 'Object') + ' (Copy)',
                                width: activeObject.originalWidth,
                                height: activeObject.originalHeight,
                                realWidthInches: activeObject.realWidthInches,
                                realHeightInches: activeObject.realHeightInches,
                                displayWidthPixels: activeObject.displayWidthPixels,
                                displayHeightPixels: activeObject.displayHeightPixels,
                                fabricObject: cloned,
                                quantity: 1,
                                dpi: activeObject.fileDPI || this.dpi
                            });

                            this.updateImageList();
                        }

                        this.fabricCanvas.add(cloned);
                        this.fabricCanvas.setActiveObject(cloned);
                        this.fabricCanvas.renderAll();
                        this.updateStats();
                        this.showStatus('Object duplicated with proper spacing', 'success');
                        console.log('Object duplicated successfully with proper spacing');
                    });
                } else if (activeObject && activeObject.isGrid) {
                    this.showStatus('Cannot duplicate grid elements', 'error');
                }
            }

            deleteSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject) {
                    this.fabricCanvas.remove(activeObject);
                    this.fabricCanvas.renderAll();
                    this.updateStats();
                    this.showStatus('Object deleted', 'success');
                }
            }

            toggleGrid() {
                this.isGridVisible = !this.isGridVisible;
                const gridBtn = document.getElementById('grid-toggle');

                if (this.isGridVisible) {
                    gridBtn?.classList.add('active');
                    this.drawGrid();
                } else {
                    gridBtn?.classList.remove('active');
                    // Remove grid lines
                    const existingGrid = this.fabricCanvas.getObjects().filter(obj => obj.isGrid);
                    existingGrid.forEach(obj => this.fabricCanvas.remove(obj));
                    this.fabricCanvas.renderAll();
                }
            }

            // Action methods
            autoNest() {
                console.log('Auto-nest triggered - creating copies based on quantities');
                this.showStatus('Auto-nesting images based on quantities...', 'info');

                // Remove all existing quantity copies first
                const objectsToRemove = [];
                this.fabricCanvas.forEachObject((obj) => {
                    if (obj.isQuantityCopy) {
                        objectsToRemove.push(obj);
                    }
                });
                objectsToRemove.forEach(obj => this.fabricCanvas.remove(obj));

                // Get sheet dimensions
                const [sheetWidthInches, sheetHeightInches] = this.getSheetDimensions();
                console.log(`Auto-nesting on ${sheetWidthInches}" × ${sheetHeightInches}" sheet`);

                let totalCopiesCreated = 0;
                let currentX = 0;
                let currentY = 0;
                let rowHeight = 0;

                // Process each image with its quantity
                this.uploadedImages.forEach((imageData, imageIndex) => {
                    const quantity = imageData.quantity || 1;
                    const imageWidthInches = parseFloat(imageData.realWidthInches);
                    const imageHeightInches = parseFloat(imageData.realHeightInches);
                    const pixelsPerInch = this.displayScale;

                    console.log(`Processing ${imageData.name}: ${quantity} copies, size: ${imageWidthInches}" × ${imageHeightInches}"`);

                    // Create copies for this image (quantity - 1, since original exists)
                    for (let copy = 1; copy < quantity; copy++) {
                        // Check if current position + image width exceeds sheet width
                        if (currentX + imageWidthInches > sheetWidthInches) {
                            // Move to next row
                            currentX = 0;
                            currentY += rowHeight;
                            rowHeight = 0;
                        }

                        // Check if current position + image height exceeds sheet height
                        if (currentY + imageHeightInches > sheetHeightInches) {
                            console.log(`Cannot fit more copies - sheet full at ${currentY.toFixed(3)}" height`);
                            break;
                        }

                        // Clone the original image
                        const originalObj = imageData.fabricObject;
                        originalObj.clone((cloned) => {
                            // Position at current location
                            const pixelX = currentX * pixelsPerInch;
                            const pixelY = currentY * pixelsPerInch;

                            // Snap to grid
                            const gridSpacing = this.getGridSpacing();
                            const snappedX = this.snapToGrid(pixelX, gridSpacing);
                            const snappedY = this.snapToGrid(pixelY, gridSpacing);

                            cloned.set({
                                left: snappedX,
                                top: snappedY,
                                // Preserve metadata
                                originalWidth: originalObj.originalWidth,
                                originalHeight: originalObj.originalHeight,
                                realWidthInches: originalObj.realWidthInches,
                                realHeightInches: originalObj.realHeightInches,
                                displayWidthPixels: originalObj.displayWidthPixels,
                                displayHeightPixels: originalObj.displayHeightPixels,
                                fileName: originalObj.fileName,
                                fileDPI: originalObj.fileDPI,
                                isExactSize: true,
                                isQuantityCopy: true,
                                parentImageIndex: imageIndex
                            });

                            this.fabricCanvas.add(cloned);
                            totalCopiesCreated++;

                            console.log(`Copy ${copy} of ${imageData.name} placed at (${currentX.toFixed(3)}", ${currentY.toFixed(3)})`);
                        });

                        // Update position for next image
                        currentX += imageWidthInches;
                        rowHeight = Math.max(rowHeight, imageHeightInches);
                    }
                });

                this.fabricCanvas.renderAll();
                this.updateStats();
                this.showStatus(`Auto-nested: Created ${totalCopiesCreated} copies with optimal layout`, 'success');
                console.log(`Auto-nest complete: ${totalCopiesCreated} copies created`);
            }

            optimizeLayout() {
                this.showStatus('Optimizing layout...', 'info');
                // Placeholder for layout optimization
                setTimeout(() => {
                    this.showStatus('Layout optimized', 'success');
                    this.updateStats();
                }, 1000);
            }

            previewPrint() {
                this.showStatus('Generating print preview...', 'info');
                // Show download button
                document.getElementById('download-btn')?.classList.remove('hidden');
                this.showStatus('Print preview ready', 'success');
            }

            downloadGangSheet() {
                if (!this.fabricCanvas) {
                    this.showStatus('Canvas not ready for export', 'error');
                    return;
                }

                // Calculate the exact multiplier to get print resolution
                const currentCanvasWidth = this.fabricCanvas.getWidth();
                const printMultiplier = this.printWidthPixels / currentCanvasWidth;

                console.log('Exporting at EXACT print resolution:');
                console.log(`Canvas size: ${currentCanvasWidth}x${this.fabricCanvas.getHeight()}`);
                console.log(`Print size: ${this.printWidthPixels}x${this.printHeightPixels} pixels`);
                console.log(`Multiplier: ${printMultiplier.toFixed(4)}`);
                console.log(`Final DPI: ${this.dpi}`);

                // Hide grid for export
                const gridObjects = this.fabricCanvas.getObjects().filter(obj => obj.isGrid);
                gridObjects.forEach(obj => obj.set('opacity', 0));
                this.fabricCanvas.renderAll();

                try {
                    const dataURL = this.fabricCanvas.toDataURL({
                        format: 'png',
                        quality: 1,
                        multiplier: printMultiplier, // Exact multiplier for print resolution
                        enableRetinaScaling: false
                    });

                    const [sheetWidth, sheetHeight] = this.getSheetDimensions();
                    const filename = `dtf-gang-sheet-${sheetWidth}x${sheetHeight}-${this.dpi}dpi-${Date.now()}.png`;

                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = dataURL;
                    link.click();

                    this.showStatus(`Gang sheet exported at ${this.printWidthPixels}×${this.printHeightPixels}px (${this.dpi} DPI)`, 'success');
                    console.log('Export completed successfully');

                } catch (error) {
                    console.error('Export error:', error);
                    this.showStatus('Error exporting gang sheet: ' + error.message, 'error');
                }

                // Restore grid visibility
                gridObjects.forEach(obj => obj.set('opacity', obj.originalOpacity || 0.5));
                this.fabricCanvas.renderAll();
            }

            saveProject() {
                const projectData = {
                    userId: this.userId,
                    sessionId: this.sessionId,
                    sheetSize: this.sheetSize,
                    dpi: this.dpi,
                    spacing: this.spacing,
                    bleed: this.bleed,
                    images: this.uploadedImages.map(img => ({
                        name: img.name,
                        width: img.width,
                        height: img.height,
                        quantity: img.quantity || 1
                    })),
                    canvasData: JSON.stringify(this.fabricCanvas.toJSON())
                };

                // Send to server (placeholder)
                this.showStatus('Saving project...', 'info');
                console.log('Project data:', projectData);

                setTimeout(() => {
                    this.showStatus('Project saved successfully', 'success');
                }, 1000);
            }

            setupNavigation() {
                // Navigation toggle
                const navToggle = document.getElementById('nav-toggle');
                const navSidebar = document.getElementById('nav-sidebar');

                if (navToggle && navSidebar) {
                    navToggle.addEventListener('click', () => {
                        navSidebar.classList.toggle('collapsed');
                    });
                }

                // Section toggles
                document.querySelectorAll('.nav-section-header').forEach(header => {
                    header.addEventListener('click', (e) => {
                        // Don't toggle if clicking on popup area
                        if (e.target.closest('.nav-section-popup')) return;

                        const section = header.getAttribute('data-section');
                        const content = document.getElementById(section + '-content');
                        const arrow = header.querySelector('.nav-section-arrow');

                        if (content && arrow) {
                            const isExpanded = content.classList.contains('expanded');

                            // Close all other sections
                            document.querySelectorAll('.nav-section-content').forEach(c => {
                                c.classList.remove('expanded');
                            });
                            document.querySelectorAll('.nav-section-arrow').forEach(a => {
                                a.classList.remove('expanded');
                            });
                            document.querySelectorAll('.nav-section-header').forEach(h => {
                                h.classList.remove('active');
                            });

                            // Toggle current section
                            if (!isExpanded) {
                                content.classList.add('expanded');
                                arrow.classList.add('expanded');
                                header.classList.add('active');
                            }
                        }
                    });
                });
            }
        }

        // Initialize the DTF Builder when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded - Starting DTF Builder initialization');
            console.log('Fabric available:', typeof fabric !== 'undefined');

            try {
                window.dtfBuilder = new ProfessionalDTFBuilder();
                console.log('DTF Builder instance created successfully');
            } catch (error) {
                console.error('Error creating DTF Builder:', error);
                alert('Error initializing DTF Builder: ' + error.message);
            }
        });
    </script>
</body>
</html>
