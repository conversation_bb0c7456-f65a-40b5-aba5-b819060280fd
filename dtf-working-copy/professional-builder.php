<?php
/**
 * Professional DTF Gang Sheet Builder - PHP Version
 * Enhanced with server-side processing and database integration
 */

// Include configuration and database abstraction
require_once 'includes/config.php';
require_once 'includes/database-abstraction.php';
require_once 'includes/functions.php';

// Initialize session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get or create user
$user = dtf_get_or_create_user();

// Get user projects for quick access
$recent_projects = [];
if ($user) {
    try {
        $db = dtf_db();
        $recent_projects = $db->fetchAll(
            'projects',
            ['user_id' => $user['id']],
            'updated_at DESC',
            5
        );
    } catch (Exception $e) {
        dtf_log('ERROR', 'Failed to load recent projects', ['error' => $e->getMessage()]);
    }
}

// Page title and meta
$page_title = "Professional DTF Gang Sheet Builder";
$page_description = "Industry-standard DTF gang sheet creation with auto-nesting and professional features";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    
    <!-- Professional DTF Builder Styles -->
    <link rel="stylesheet" href="assets/css/professional-builder.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Fabric.js for advanced canvas manipulation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 10px;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header p {
            margin: 0;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .header-info {
            text-align: left;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .header-actions a {
            color: #00d4ff;
            text-decoration: none;
            font-size: 0.9rem;
            padding: 8px 15px;
            border: 1px solid #00d4ff;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .header-actions a:hover {
            background: #00d4ff;
            color: #2c3e50;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 0;
            height: calc(100vh - 80px);
            width: 100%;
        }

        /* Collapsible Navigation Sidebar */
        .nav-sidebar {
            background: #2c3e50;
            color: white;
            overflow-y: auto;
            transition: all 0.3s ease;
            border-radius: 10px 0 0 10px;
        }

        .nav-sidebar.collapsed {
            width: 60px;
        }

        .nav-header {
            padding: 15px 20px;
            background: #34495e;
            border-bottom: 1px solid #3d566e;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-brand {
            font-weight: 600;
            font-size: 1.1rem;
            color: #00d4ff;
        }

        .nav-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .nav-toggle:hover {
            background: rgba(255,255,255,0.1);
        }

        .nav-section {
            border-bottom: 1px solid #3d566e;
        }

        .nav-section-header {
            padding: 15px 20px;
            background: #34495e;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background 0.3s ease;
            user-select: none;
        }

        .nav-section-header:hover {
            background: #3d566e;
        }

        .nav-section-header.active {
            background: #00d4ff;
            color: #2c3e50;
        }

        .nav-section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .nav-section-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .nav-section-arrow {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .nav-section-arrow.expanded {
            transform: rotate(90deg);
        }

        .nav-section-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #2c3e50;
        }

        .nav-section-content.expanded {
            max-height: 500px;
        }

        .nav-section-content .control-section {
            padding: 20px;
            border-bottom: none;
            margin-bottom: 0;
        }

        .canvas-area {
            background: white;
            border-radius: 0 8px 8px 0;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            width: 100%;
            min-height: calc(100vh - 80px);
        }

        /* Collapsed sidebar styles */
        .nav-sidebar.collapsed {
            width: 60px;
        }

        .nav-sidebar.collapsed .nav-section-title span,
        .nav-sidebar.collapsed .nav-section-arrow,
        .nav-sidebar.collapsed .nav-brand {
            display: none;
        }

        .nav-sidebar.collapsed .nav-section-content {
            display: none;
        }

        .nav-sidebar.collapsed .nav-section-header {
            padding: 15px 10px;
            justify-content: center;
            position: relative;
        }

        .nav-sidebar.collapsed .nav-header {
            padding: 15px 10px;
            justify-content: center;
        }

        /* Canvas-style tooltips for collapsed sidebar */
        .nav-sidebar.collapsed .nav-section-header::before {
            content: attr(data-tooltip);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(4px);
        }

        .nav-sidebar.collapsed .nav-section-header:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Canva-style popup panels - positioned right next to icons */
        .nav-section-popup {
            position: absolute;
            left: 60px; /* Right next to the 60px collapsed sidebar */
            top: 0;
            width: 320px;
            max-height: 500px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .nav-sidebar.collapsed .nav-section-header:hover .nav-section-popup {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .nav-section-popup .popup-header {
            padding: 15px 18px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-section-popup .popup-header::before {
            content: '';
            width: 3px;
            height: 16px;
            background: #3498db;
            border-radius: 2px;
        }

        .nav-section-popup .control-section {
            padding: 18px;
            border-bottom: none;
            margin-bottom: 0;
            background: white;
        }

        /* Hide tooltip when popup is shown */
        .nav-sidebar.collapsed .nav-section-header:hover::before {
            display: none;
        }

        /* Popup arrow indicator pointing from icon */
        .nav-section-popup::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 20px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-right: 6px solid white;
            filter: drop-shadow(-1px 0 2px rgba(0,0,0,0.1));
        }

        /* Professional Controls */
        .control-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .control-section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Settings Grid */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            display: block;
            font-weight: 500;
            margin-bottom: 5px;
            color: #555;
            font-size: 0.9rem;
        }

        .control-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .control-input:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Upload Zone */
        .upload-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 8px;
            padding: 30px 15px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 500;
        }

        .upload-zone:hover {
            border-color: #3498db;
            background: #ecf0f1;
            color: #2c3e50;
        }

        .upload-zone small {
            color: #555;
            font-weight: 400;
        }

        .upload-zone.has-files {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        /* Image List */
        .image-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
        }

        .image-item:last-child {
            border-bottom: none;
        }

        .image-thumb {
            width: 30px;
            height: 30px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }

        .image-info {
            flex: 1;
        }

        .image-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .image-size {
            color: #7f8c8d;
            font-size: 0.75rem;
        }

        .image-real-size {
            font-size: 0.7rem;
            color: #00d4ff;
            font-weight: 600;
            margin-top: 2px;
        }

        .quantity-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
            font-size: 0.8rem;
        }

        /* Recent Projects */
        .recent-projects {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .project-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.85rem;
        }

        .project-item:last-child {
            border-bottom: none;
        }

        .project-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .project-date {
            color: #6c757d;
            font-size: 0.75rem;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
            width: 100%;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .hidden { display: none; }

        /* Canvas */
        .canvas-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 100%;
            height: calc(100vh - 200px);
        }

        /* Canvas Toolbar */
        .canvas-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .canvas-tools {
            display: flex;
            gap: 10px;
        }

        .tool-button {
            background: white;
            border: 1px solid #ddd;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            color: #333;
        }

        .tool-button:hover {
            background: #f0f0f0;
            border-color: #3498db;
        }

        .tool-button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .zoom-level {
            font-weight: 600;
            color: #333;
        }

        #design-canvas {
            display: block;
            width: 100%;
            height: calc(100vh - 280px);
            background: white;
            cursor: crosshair;
            border: none;
        }

        .canvas-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 0.85rem;
            color: #666;
        }

        /* Status */
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 0.85rem;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Professional Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #2c3e50;
            border-radius: 8px;
        }

        .stat-card {
            background: #34495e;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #4a5f7a;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: #3d566e;
            transform: translateY(-2px);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #bdc3c7;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Settings Grid */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Navigation specific control styling */
        .nav-section-content .control-input {
            background: #34495e;
            border: 1px solid #4a5f7a;
            color: white;
        }

        .nav-section-content .control-input:focus {
            border-color: #00d4ff;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }

        .nav-section-content .control-label {
            color: #bdc3c7;
            font-size: 0.85rem;
        }

        .nav-section-content .btn {
            margin-bottom: 8px;
        }

        .nav-section-content .checkbox-group {
            margin-bottom: 8px;
        }

        .nav-section-content .checkbox-group label {
            color: #bdc3c7;
        }

        /* Canva-style popup control styling */
        .nav-section-popup .control-input {
            background: white;
            border: 1px solid #ddd;
            color: #333;
            border-radius: 6px;
            padding: 10px 12px;
            font-size: 0.9rem;
        }

        .nav-section-popup .control-input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            outline: none;
        }

        .nav-section-popup .control-label {
            color: #555;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .nav-section-popup .btn {
            margin-bottom: 10px;
            border-radius: 6px;
            font-weight: 500;
            padding: 10px 16px;
        }

        .nav-section-popup .upload-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            color: #2c3e50;
            font-weight: 500;
        }

        .nav-section-popup .upload-zone:hover {
            border-color: #3498db;
            background: #f0f8ff;
            color: #2c3e50;
        }

        .nav-section-popup .upload-zone small {
            color: #555;
            font-weight: 400;
        }
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-info">
                <h1>🏭 Professional DTF Gang Sheet Builder</h1>
                <p>Industry-standard features for professional DTF printing</p>
                <?php if ($user): ?>
                    <small style="opacity: 0.6;">Welcome back, User #<?php echo $user['id']; ?> | Session: <?php echo substr($user['session_id'], 0, 8); ?>...</small>
                <?php endif; ?>
            </div>
            <div class="header-actions">
                <a href="#" onclick="window.open('/shop', '_blank')">🛍️ Shop</a>
                <a href="#" onclick="window.open('/services', '_blank')">🔧 Services</a>
                <a href="#" onclick="window.open('/cart', '_blank')">🛒 Cart</a>
            </div>
        </div>

        <div class="main-grid">
            <!-- Collapsible Navigation Sidebar -->
            <div class="nav-sidebar" id="nav-sidebar">
                <!-- Navigation Header -->
                <div class="nav-header">
                    <div class="nav-brand">🏭 CYPTSHOP</div>
                    <button class="nav-toggle" id="nav-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- File Management Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="file-management" data-tooltip="File Management">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-folder"></i>
                            <span>File Management</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-folder"></i>
                                File Management
                            </div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Upload Images</label>
                                    <div id="upload-zone-popup" class="upload-zone" style="margin-bottom: 15px;">
                                        <div style="font-size: 1.1rem; margin-bottom: 5px;">📤 Drop files or click to upload</div>
                                        <small style="color: #666;">PNG, JPG, PDF, AI, EPS files supported</small>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Recent Files</label>
                                    <div style="font-size: 0.85rem; color: #666; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                                        No recent files
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="file-management-content">
                        <div class="control-section">
                            <div id="upload-zone" class="upload-zone">
                                <div>📤 Drop files or click to upload</div>
                                <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                            </div>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">

                            <!-- UPLOAD PROGRESS BAR -->
                            <div id="upload-progress" class="hidden" style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 6px; padding: 15px; margin: 10px 0;">
                                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 8px;">📤 Uploading Files...</div>
                                <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden; margin-bottom: 8px;">
                                    <div id="progress-bar" style="background: linear-gradient(90deg, #28a745, #20c997); height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                                </div>
                                <div id="progress-text" style="font-size: 0.9rem; color: #2c3e50;">Preparing upload...</div>
                            </div>

                            <!-- FILE DETAILS - Shows immediately after upload -->
                            <div id="file-details-display" class="hidden" style="background: #e8f5e8; border: 2px solid #27ae60; border-radius: 6px; padding: 12px; margin: 10px 0;">
                                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 8px;">📄 File Details:</div>
                                <div id="file-info-content" style="font-family: monospace; font-size: 0.9rem; color: #2c3e50;">
                                    <!-- File info populated here -->
                                </div>
                            </div>

                            <!-- QUANTITY SELECTOR - Shows after upload -->
                            <div id="quantity-controls" class="hidden" style="background: #fff3cd; border: 2px solid #ffc107; border-radius: 6px; padding: 15px; margin: 10px 0;">
                                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 10px;">📊 Quantity Selector:</div>
                                <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 12px;">
                                    <label style="font-weight: 600;">Copies:</label>
                                    <input type="number" id="copies-input" value="1" min="1" max="999"
                                           style="width: 80px; text-align: center; font-size: 1.2rem; padding: 6px; border: 2px solid #ffc107; border-radius: 4px; font-weight: 600;">
                                </div>
                                <div style="display: flex; gap: 10px;">
                                    <button id="duplicate-copies-btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: 600; cursor: pointer;">
                                        📋 Duplicate
                                    </button>
                                    <button id="fill-sheet-btn" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: 600; cursor: pointer;">
                                        🔄 Fill Entire Sheet
                                    </button>
                                </div>
                            </div>

                            <div id="image-list" class="image-list hidden"></div>

                            <?php if (!empty($recent_projects)): ?>
                            <div style="margin-top: 15px;">
                                <div class="section-title" style="color: #bdc3c7; font-size: 0.9rem;">Recent Projects</div>
                                <div class="recent-projects">
                                    <?php foreach (array_slice($recent_projects, 0, 3) as $project): ?>
                                    <div class="project-item" style="padding: 8px; border-bottom: 1px solid #3d566e;">
                                        <div>
                                            <div class="project-name" style="color: white; font-size: 0.85rem;"><?php echo htmlspecialchars($project['name']); ?></div>
                                            <div class="project-date" style="color: #95a5a6; font-size: 0.75rem;"><?php echo date('M j', strtotime($project['updated_at'])); ?></div>
                                        </div>
                                        <button class="btn btn-sm" style="padding: 4px 8px; font-size: 0.75rem;" onclick="loadProject(<?php echo $project['id']; ?>)">Load</button>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="sheet-config" data-tooltip="Sheet Configuration">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-ruler-combined"></i>
                            <span>Sheet Configuration</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-ruler-combined"></i>
                                Sheet Configuration
                            </div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Sheet Size</label>
                                    <select class="control-input">
                                        <option>22" × 72" (Popular)</option>
                                        <option>22" × 60"</option>
                                        <option>22" × 48"</option>
                                        <option>Custom Size</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Print Quality</label>
                                    <select class="control-input">
                                        <option>300 DPI (Standard)</option>
                                        <option>600 DPI (High Quality)</option>
                                        <option>1200 DPI (Premium)</option>
                                    </select>
                                </div>
                                <div style="font-size: 0.85rem; color: #666; padding: 10px; background: #f0f8ff; border-radius: 6px; border-left: 3px solid #3498db;">
                                    💡 22" × 72" is the most popular size for DTF gang sheets
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="sheet-config-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Sheet Size</label>
                                <select id="sheet-size" class="control-input">
                                    <option value="22x12">22" × 12" (Standard)</option>
                                    <option value="22x24">22" × 24"</option>
                                    <option value="22x36">22" × 36"</option>
                                    <option value="22x48">22" × 48"</option>
                                    <option value="22x60">22" × 60"</option>
                                    <option value="22x72" selected>22" × 72" (Popular)</option>
                                    <option value="22x100">22" × 100"</option>
                                    <option value="22x120">22" × 120" (Max)</option>
                                </select>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">DPI</label>
                                    <select id="dpi" class="control-input">
                                        <option value="150">150 DPI</option>
                                        <option value="300" selected>300 DPI</option>
                                        <option value="600">600 DPI</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Color Mode</label>
                                    <select id="color-mode" class="control-input">
                                        <option value="cmyk" selected>CMYK</option>
                                        <option value="rgb">RGB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="advanced-settings" data-tooltip="Advanced Settings">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-cogs"></i>
                            <span>Advanced Settings</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">
                                <i class="fas fa-cogs"></i>
                                Advanced Settings
                            </div>
                            <div class="control-section">
                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Spacing (mm)</label>
                                        <input type="number" class="control-input" value="3" min="0" max="10">
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Bleed (mm)</label>
                                        <input type="number" class="control-input" value="1" min="0" max="5">
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Optimization</label>
                                    <div style="display: flex; flex-direction: column; gap: 8px;">
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox" checked> Auto-rotate for best fit
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox" checked> Maintain aspect ratio
                                        </label>
                                        <label style="display: flex; align-items: center; gap: 8px; font-size: 0.9rem;">
                                            <input type="checkbox"> Add safety margins
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="advanced-settings-content">
                        <div class="control-section">
                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">Spacing (mm)</label>
                                    <input type="number" id="spacing" class="control-input" value="3" min="0" max="20" step="0.5">
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Bleed (mm)</label>
                                    <input type="number" id="bleed" class="control-input" value="1" min="0" max="10" step="0.5">
                                </div>
                            </div>

                            <div class="control-group">
                                <label class="control-label">Nesting Algorithm</label>
                                <select id="nesting-algorithm" class="control-input">
                                    <option value="efficiency" selected>Maximum Efficiency</option>
                                    <option value="speed">Fastest Processing</option>
                                    <option value="uniform">Uniform Spacing</option>
                                    <option value="rows">Row-by-Row</option>
                                </select>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="auto-rotate" checked>
                                <label for="auto-rotate">Auto-rotate for optimal fit</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="maintain-aspect" checked>
                                <label for="maintain-aspect">Maintain aspect ratio</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="add-margins" checked>
                                <label for="add-margins">Add safety margins</label>
                            </div>

                            <div class="checkbox-group">
                                <input type="checkbox" id="add-crop-marks">
                                <label for="add-crop-marks">Add crop marks</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Features Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="professional-features" data-tooltip="Professional Features">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-star"></i>
                            <span>Professional Features</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Professional Features</div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Export Format</label>
                                    <select class="control-input" style="font-size: 0.8rem;">
                                        <option>PNG (Recommended)</option>
                                        <option>PDF (Print Ready)</option>
                                        <option>TIFF (Professional)</option>
                                    </select>
                                </div>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    Export and project management
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="professional-features-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Export Format</label>
                                <select id="export-format" class="control-input">
                                    <option value="png" selected>PNG (Recommended)</option>
                                    <option value="pdf">PDF (Print Ready)</option>
                                    <option value="jpg">JPEG (Compressed)</option>
                                    <option value="tiff">TIFF (Professional)</option>
                                </select>
                            </div>

                            <button id="save-project-btn" class="btn btn-primary">
                                💾 Save Project
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="quick-actions" data-tooltip="Quick Actions">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-bolt"></i>
                            <span>Quick Actions</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Quick Actions</div>
                            <div class="control-section">
                                <button class="btn btn-primary" style="margin-bottom: 8px; font-size: 0.8rem;">
                                    🧩 Auto-Nest Images
                                </button>
                                <button class="btn btn-primary" style="margin-bottom: 8px; font-size: 0.8rem;">
                                    ⚡ Optimize Layout
                                </button>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    One-click automation tools
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="quick-actions-content">
                        <div class="control-section">
                            <button id="auto-nest-btn" class="btn btn-primary" disabled>
                                🧩 Auto-Nest Images
                            </button>
                            <button id="optimize-btn" class="btn btn-primary" disabled>
                                ⚡ Optimize Layout
                            </button>
                            <button id="preview-btn" class="btn btn-primary" disabled>
                                👁️ Preview Print
                            </button>
                            <button id="download-btn" class="btn btn-success hidden">
                                📥 Download Gang Sheet
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Add to Cart Section -->
                <div class="nav-section">
                    <div class="nav-section-header" data-section="add-to-cart" data-tooltip="Add to Cart">
                        <div class="nav-section-title">
                            <i class="nav-section-icon fas fa-shopping-cart"></i>
                            <span>Add to Cart</span>
                        </div>
                        <i class="nav-section-arrow fas fa-chevron-right"></i>
                        <div class="nav-section-popup">
                            <div class="popup-header">Add to Cart</div>
                            <div class="control-section">
                                <div class="control-group">
                                    <label class="control-label">Quantity</label>
                                    <input type="number" class="control-input" value="1" style="font-size: 0.8rem;">
                                </div>
                                <button class="btn btn-success" style="font-size: 0.8rem;">
                                    🛒 Add to Cart - $0.00
                                </button>
                                <div style="font-size: 0.8rem; color: #bdc3c7;">
                                    Purchase gang sheet printing
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-section-content" id="add-to-cart-content">
                        <div class="control-section">
                            <div class="control-group">
                                <label class="control-label">Quantity</label>
                                <input type="number" id="cart-quantity" class="control-input" value="1" min="1" max="100">
                            </div>
                            <div class="control-group">
                                <label class="control-label">Rush Order</label>
                                <select id="rush-order" class="control-input">
                                    <option value="standard">Standard (3-5 days)</option>
                                    <option value="rush">Rush (1-2 days) +$25</option>
                                    <option value="same-day">Same Day +$50</option>
                                </select>
                            </div>
                            <button id="add-to-cart-btn" class="btn btn-success" disabled>
                                🛒 Add to Cart - $0.00
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <!-- Professional Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="image-count">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Container with Toolbar -->
                <div class="canvas-container">
                    <!-- Canvas Toolbar -->
                    <div class="canvas-toolbar">
                        <div class="canvas-tools">
                            <button id="zoom-in" class="tool-button" title="Zoom In">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button id="zoom-out" class="tool-button" title="Zoom Out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button id="zoom-fit" class="tool-button" title="Fit to View">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button id="select-tool" class="tool-button active" title="Select Tool">
                                <i class="fas fa-mouse-pointer"></i>
                            </button>
                            <button id="move-tool" class="tool-button" title="Move Tool">
                                <i class="fas fa-arrows-alt"></i>
                            </button>
                            <button id="rotate-tool" class="tool-button" title="Rotate Tool">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button id="duplicate-tool" class="tool-button" title="Duplicate Selected">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button id="delete-tool" class="tool-button" title="Delete Selected">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button id="grid-toggle" class="tool-button" title="Toggle Grid">
                                <i class="fas fa-th"></i>
                            </button>
                        </div>

                        <div class="zoom-controls">
                            <span>Zoom:</span>
                            <span id="zoom-level" class="zoom-level">100%</span>
                        </div>
                    </div>

                    <!-- Design Canvas with Fabric.js -->
                    <canvas id="design-canvas" width="800" height="600"></canvas>

                    <!-- Canvas Info -->
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">22" × 72"</span></span>
                        <span>DPI: <span id="current-dpi">300</span></span>
                        <span>Status: <span id="canvas-status">Ready</span></span>
                    </div>
                </div>

                <!-- Status Messages -->
                <div id="status-message" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Include Professional DTF Builder JavaScript -->
    <script src="assets/js/dtf-builder-core.js"></script>
    <script src="assets/js/dtf-builder-auto-nest.js"></script>

    <script>
        // Initialize Professional DTF Builder with Fabric.js and PHP integration
        class ProfessionalDTFBuilder {
            constructor() {
                this.fabricCanvas = null;
                this.uploadedImages = [];
                this.sheetSize = '22x72';
                this.dpi = 300;
                this.spacing = 3; // mm
                this.bleed = 1; // mm
                this.autoRotate = true;
                this.maintainAspect = true;
                this.addMargins = true;
                this.currentTool = 'select';
                this.zoomLevel = 1;
                this.isGridVisible = true;
                this.isPanning = false;
                this.lastPanPoint = null;
                this.dpi = 300; // Standard DTF DPI
                this.userId = <?php echo json_encode($user['id'] ?? null); ?>;
                this.sessionId = <?php echo json_encode($user['session_id'] ?? null); ?>;

                this.init();
            }

            init() {
                this.initFabricCanvas();
                this.setupEvents();
                this.setupToolbar();
                this.setupNavigation();
                this.updateStats();

                // Set grid toggle button as active by default
                const gridBtn = document.getElementById('grid-toggle');
                if (gridBtn) {
                    gridBtn.classList.add('active');
                }

                // Force grid to show on initialization
                setTimeout(() => {
                    this.addGrid();
                }, 500);

                this.showStatus('Professional DTF Builder with Fabric.js ready', 'info');
            }

            setupNavigation() {
                // Navigation toggle functionality
                const navToggle = document.getElementById('nav-toggle');
                const navSidebar = document.getElementById('nav-sidebar');

                navToggle.addEventListener('click', () => {
                    navSidebar.classList.toggle('collapsed');
                });

                // Section toggle functionality
                const sectionHeaders = document.querySelectorAll('.nav-section-header');

                sectionHeaders.forEach(header => {
                    header.addEventListener('click', () => {
                        const section = header.getAttribute('data-section');
                        const content = document.getElementById(section + '-content');
                        const arrow = header.querySelector('.nav-section-arrow');

                        // Close other sections
                        sectionHeaders.forEach(otherHeader => {
                            if (otherHeader !== header) {
                                const otherSection = otherHeader.getAttribute('data-section');
                                const otherContent = document.getElementById(otherSection + '-content');
                                const otherArrow = otherHeader.querySelector('.nav-section-arrow');

                                otherContent.classList.remove('expanded');
                                otherArrow.classList.remove('expanded');
                                otherHeader.classList.remove('active');
                            }
                        });

                        // Toggle current section
                        content.classList.toggle('expanded');
                        arrow.classList.toggle('expanded');
                        header.classList.toggle('active');
                    });
                });

                // Open File Management by default
                const fileManagementHeader = document.querySelector('[data-section="file-management"]');
                if (fileManagementHeader) {
                    fileManagementHeader.click();
                }
            }

            initFabricCanvas() {
                // Get canvas container dimensions
                const canvasContainer = document.querySelector('.canvas-container');
                const containerWidth = canvasContainer.clientWidth - 2; // Account for borders
                const containerHeight = canvasContainer.clientHeight - 120; // Account for toolbar and info

                // Initialize Fabric.js canvas with full container size
                this.fabricCanvas = new fabric.Canvas('design-canvas', {
                    backgroundColor: '#ffffff',
                    selection: true,
                    preserveObjectStacking: true,
                    width: containerWidth,
                    height: containerHeight
                });

                // Set canvas size based on sheet dimensions
                this.updateCanvasSize();

                // Add grid
                this.addGrid();

                // Make canvas responsive
                this.makeCanvasResponsive();
            }

            makeCanvasResponsive() {
                const canvasContainer = document.querySelector('.canvas-container');
                const resizeCanvas = () => {
                    const containerWidth = canvasContainer.clientWidth - 2;
                    const containerHeight = canvasContainer.clientHeight - 120;

                    this.fabricCanvas.setDimensions({
                        width: containerWidth,
                        height: containerHeight
                    });

                    this.fabricCanvas.renderAll();
                };

                // Resize on window resize
                window.addEventListener('resize', resizeCanvas);

                // Initial resize
                setTimeout(resizeCanvas, 100);
            }

            updateCanvasSize() {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);

                // Get available canvas space
                const canvasContainer = document.querySelector('.canvas-container');
                const availableWidth = canvasContainer.clientWidth - 40; // Account for padding
                const availableHeight = canvasContainer.clientHeight - 120; // Account for toolbar and info

                // Calculate scale to fit the sheet proportionally in available space
                // Use a reasonable pixels-per-inch scale for visibility
                const basePixelsPerInch = 15; // Base scale for visibility
                const scaleX = availableWidth / (sheetWidth * basePixelsPerInch);
                const scaleY = availableHeight / (sheetHeight * basePixelsPerInch);
                const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond base

                // Calculate final pixels per inch
                const pixelsPerInch = basePixelsPerInch * scale;

                // Calculate canvas size to represent EXACTLY the sheet dimensions
                const canvasWidth = Math.round(sheetWidth * pixelsPerInch);
                const canvasHeight = Math.round(sheetHeight * pixelsPerInch);

                // Store values for grid calculations
                this.pixelsPerInch = pixelsPerInch;
                this.sheetWidthInches = sheetWidth;
                this.sheetHeightInches = sheetHeight;

                if (this.fabricCanvas) {
                    this.fabricCanvas.setDimensions({
                        width: canvasWidth,
                        height: canvasHeight
                    });

                    // Center the canvas in the container
                    const canvas = this.fabricCanvas.getElement();
                    canvas.style.display = 'block';
                    canvas.style.margin = '0 auto';

                    this.addGrid();
                }

                // Update sheet dimensions display
                document.getElementById('sheet-dimensions').textContent = `${sheetWidth}" × ${sheetHeight}"`;
            }

            addGrid() {
                if (!this.isGridVisible) return;

                // Remove existing grid
                this.fabricCanvas.getObjects().forEach(obj => {
                    if (obj.isGrid) {
                        this.fabricCanvas.remove(obj);
                    }
                });

                const canvasWidth = this.fabricCanvas.width;
                const canvasHeight = this.fabricCanvas.height;

                // Get sheet dimensions
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);

                // Calculate pixels per inch - canvas represents the exact sheet size
                const pixelsPerInch = canvasWidth / sheetWidth;

                // Make grid clearly visible
                const gridColor = '#d0d0d0';
                const gridWidth = 1;

                // Create vertical lines for each inch (0, 1, 2, 3... up to sheetWidth)
                for (let inch = 0; inch <= sheetWidth; inch++) {
                    const x = inch * pixelsPerInch;
                    const line = new fabric.Line([x, 0, x, canvasHeight], {
                        stroke: gridColor,
                        strokeWidth: gridWidth,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.fabricCanvas.add(line);
                    this.fabricCanvas.sendToBack(line);
                }

                // Create horizontal lines for each inch (0, 1, 2, 3... up to sheetHeight)
                for (let inch = 0; inch <= sheetHeight; inch++) {
                    const y = inch * pixelsPerInch;
                    const line = new fabric.Line([0, y, canvasWidth, y], {
                        stroke: gridColor,
                        strokeWidth: gridWidth,
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.fabricCanvas.add(line);
                    this.fabricCanvas.sendToBack(line);
                }

                // Add grid labels showing inch numbers
                this.addGridLabels(pixelsPerInch, sheetWidth, sheetHeight);

                // Force render
                this.fabricCanvas.renderAll();
            }

            addGridLabels(pixelsPerInch, sheetWidth, sheetHeight) {
                // Add inch markers along the top edge (width) - 1", 2", 3"... up to sheetWidth"
                for (let inch = 1; inch <= sheetWidth; inch++) {
                    const x = inch * pixelsPerInch;

                    // Top ruler marks
                    const topText = new fabric.Text(inch.toString() + '"', {
                        left: x - 8,
                        top: 5,
                        fontSize: 10,
                        fill: '#666',
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.fabricCanvas.add(topText);
                    this.fabricCanvas.sendToBack(topText);
                }

                // Add inch markers along the left edge (height) - 1", 2", 3"... up to sheetHeight"
                for (let inch = 1; inch <= sheetHeight; inch++) {
                    const y = inch * pixelsPerInch;

                    // Left ruler marks
                    const leftText = new fabric.Text(inch.toString() + '"', {
                        left: 5,
                        top: y - 5,
                        fontSize: 10,
                        fill: '#666',
                        selectable: false,
                        evented: false,
                        isGrid: true
                    });
                    this.fabricCanvas.add(leftText);
                    this.fabricCanvas.sendToBack(leftText);
                }
            }

            // Helper method to convert millimeters to pixels
            mmToPixels(mm) {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.fabricCanvas.width;
                const pixelsPerInch = canvasWidth / sheetWidth; // Exact pixels per inch
                return (mm / 25.4) * pixelsPerInch; // 25.4 mm per inch
            }

            // Helper method to convert inches to pixels
            inchesToPixels(inches) {
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.fabricCanvas.width;
                const pixelsPerInch = canvasWidth / sheetWidth; // Exact pixels per inch
                return inches * pixelsPerInch;
            }

            toggleGrid() {
                this.isGridVisible = !this.isGridVisible;

                if (this.isGridVisible) {
                    this.addGrid();
                } else {
                    // Remove grid lines
                    this.fabricCanvas.getObjects().forEach(obj => {
                        if (obj.isGrid) {
                            this.fabricCanvas.remove(obj);
                        }
                    });
                }

                this.fabricCanvas.renderAll();

                // Update button appearance
                const gridBtn = document.getElementById('grid-toggle');
                if (gridBtn) {
                    gridBtn.classList.toggle('active', this.isGridVisible);
                    gridBtn.title = this.isGridVisible ? 'Hide Grid' : 'Show Grid';
                }
            }

            setupPanMode() {
                this.currentTool = 'pan';
                this.fabricCanvas.selection = false;
                this.fabricCanvas.defaultCursor = 'grab';
                this.fabricCanvas.hoverCursor = 'grab';
                this.fabricCanvas.moveCursor = 'grabbing';

                // Add pan event listeners
                this.fabricCanvas.on('mouse:down', this.onPanStart.bind(this));
                this.fabricCanvas.on('mouse:move', this.onPanMove.bind(this));
                this.fabricCanvas.on('mouse:up', this.onPanEnd.bind(this));
            }

            setupSelectMode() {
                this.currentTool = 'select';
                this.fabricCanvas.selection = true;
                this.fabricCanvas.defaultCursor = 'default';
                this.fabricCanvas.hoverCursor = 'move';
                this.fabricCanvas.moveCursor = 'move';

                // Remove pan event listeners
                this.fabricCanvas.off('mouse:down', this.onPanStart);
                this.fabricCanvas.off('mouse:move', this.onPanMove);
                this.fabricCanvas.off('mouse:up', this.onPanEnd);
            }

            onPanStart(opt) {
                if (this.currentTool !== 'pan') return;

                const evt = opt.e;
                this.isPanning = true;
                this.lastPanPoint = new fabric.Point(evt.clientX, evt.clientY);
                this.fabricCanvas.defaultCursor = 'grabbing';
            }

            onPanMove(opt) {
                if (this.currentTool !== 'pan' || !this.isPanning) return;

                const evt = opt.e;
                const vpt = this.fabricCanvas.viewportTransform;
                vpt[4] += evt.clientX - this.lastPanPoint.x;
                vpt[5] += evt.clientY - this.lastPanPoint.y;
                this.fabricCanvas.requestRenderAll();
                this.lastPanPoint = new fabric.Point(evt.clientX, evt.clientY);
            }

            onPanEnd(opt) {
                if (this.currentTool !== 'pan') return;

                this.isPanning = false;
                this.fabricCanvas.defaultCursor = 'grab';
            }

            setupToolbar() {
                // Zoom controls
                document.getElementById('zoom-in').onclick = () => this.zoomIn();
                document.getElementById('zoom-out').onclick = () => this.zoomOut();
                document.getElementById('zoom-fit').onclick = () => this.zoomToFit();

                // Tool buttons
                document.getElementById('select-tool').onclick = () => this.setTool('select');
                document.getElementById('move-tool').onclick = () => this.setTool('move');
                document.getElementById('rotate-tool').onclick = () => this.rotateSelected();
                document.getElementById('duplicate-tool').onclick = () => this.duplicateSelected();
                document.getElementById('delete-tool').onclick = () => this.deleteSelected();
                document.getElementById('grid-toggle').onclick = () => this.toggleGrid();
            }

            setTool(tool) {
                this.currentTool = tool;

                // Update button states
                document.querySelectorAll('.tool-button').forEach(btn => {
                    if (!btn.id.includes('zoom') && !btn.id.includes('grid')) {
                        btn.classList.remove('active');
                    }
                });
                document.getElementById(tool + '-tool').classList.add('active');

                // Update canvas interaction mode
                if (tool === 'select') {
                    this.setupSelectMode();
                } else if (tool === 'move') {
                    this.setupPanMode();
                }
            }

            zoomIn() {
                this.zoomLevel = Math.min(this.zoomLevel * 1.2, 5);
                this.fabricCanvas.setZoom(this.zoomLevel);
                this.updateZoomDisplay();
            }

            zoomOut() {
                this.zoomLevel = Math.max(this.zoomLevel / 1.2, 0.1);
                this.fabricCanvas.setZoom(this.zoomLevel);
                this.updateZoomDisplay();
            }

            zoomToFit() {
                this.zoomLevel = 1;
                this.fabricCanvas.setZoom(1);
                this.fabricCanvas.viewportTransform = [1, 0, 0, 1, 0, 0];
                this.updateZoomDisplay();
            }

            updateZoomDisplay() {
                document.getElementById('zoom-level').textContent = Math.round(this.zoomLevel * 100) + '%';
            }

            setupEvents() {
                // File upload
                const uploadZone = document.getElementById('upload-zone');
                const fileInput = document.getElementById('file-input');

                uploadZone.onclick = () => fileInput.click();
                uploadZone.ondragover = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#3498db';
                };
                uploadZone.ondragleave = () => {
                    uploadZone.style.borderColor = '#bdc3c7';
                };
                uploadZone.ondrop = (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = '#bdc3c7';
                    this.handleFiles(e.dataTransfer.files);
                };
                fileInput.onchange = (e) => this.handleFiles(e.target.files);

                // Settings
                document.getElementById('sheet-size').onchange = (e) => {
                    this.sheetSize = e.target.value;
                    this.updateCanvasSize();
                    this.showStatus(`Sheet size changed to ${this.sheetSize}"`, 'success');
                };

                document.getElementById('dpi').onchange = (e) => {
                    this.dpi = parseInt(e.target.value);
                    document.getElementById('current-dpi').textContent = this.dpi;
                    this.updateCanvas();
                };

                document.getElementById('spacing').oninput = (e) => {
                    this.spacing = parseFloat(e.target.value);
                    this.updateSpacing();
                };

                document.getElementById('bleed').oninput = (e) => {
                    this.bleed = parseFloat(e.target.value);
                    this.updateCanvas();
                };

                document.getElementById('auto-rotate').onchange = (e) => {
                    this.autoRotate = e.target.checked;
                };

                document.getElementById('maintain-aspect').onchange = (e) => {
                    this.maintainAspect = e.target.checked;
                };

                document.getElementById('add-margins').onchange = (e) => {
                    this.addMargins = e.target.checked;
                };

                // Actions
                document.getElementById('auto-nest-btn').onclick = () => this.autoNest();
                document.getElementById('optimize-btn').onclick = () => this.optimizeLayout();
                document.getElementById('download-btn').onclick = () => this.downloadGangSheet();
                document.getElementById('save-project-btn').onclick = () => this.saveProject();

                // Canvas object selection events for precise measurements
                this.fabricCanvas.on('selection:created', (e) => {
                    this.showPreciseMeasurements(e.selected[0]);
                });

                this.fabricCanvas.on('selection:updated', (e) => {
                    this.showPreciseMeasurements(e.selected[0]);
                });

                this.fabricCanvas.on('selection:cleared', () => {
                    this.hidePreciseMeasurements();
                });
            }

            async handleFiles(files) {
                if (!files || files.length === 0) return;

                console.log(`Starting upload of ${files.length} files`);
                this.showUploadProgress(0, `Preparing to upload ${files.length} file(s)...`);

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const progress = ((i + 1) / files.length) * 100;

                    if (file.type.startsWith('image/')) {
                        this.showUploadProgress(progress, `Processing ${file.name}... (${i + 1}/${files.length})`);
                        console.log(`Processing file ${i + 1}/${files.length}: ${file.name}`);

                        try {
                            await this.addImageToCanvas(file);
                            console.log(`✅ Successfully processed: ${file.name}`);
                        } catch (error) {
                            console.error(`❌ Failed to process: ${file.name}`, error);
                            this.showStatus(`Failed to process ${file.name}: ${error.message}`, 'error');
                        }
                    } else {
                        console.log(`⚠️ Skipping non-image file: ${file.name}`);
                    }
                }

                this.hideUploadProgress();
                this.showStatus(`Successfully added ${files.length} file(s)`, 'success');
                console.log(`✅ Upload complete: ${files.length} files processed`);
            }

            async addImageToCanvas(file) {
                return new Promise((resolve, reject) => {
                    console.log(`Starting to process file: ${file.name} (${file.size} bytes)`);

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        console.log(`File read successfully: ${file.name}`);
                        fabric.Image.fromURL(e.target.result, (img) => {
                            console.log(`Fabric.js image created successfully for: ${file.name}`);
                            // Get precise image dimensions
                            const originalWidth = img.width;
                            const originalHeight = img.height;

                            // Calculate real-world size in inches at 300 DPI
                            const widthInches = originalWidth / this.dpi;
                            const heightInches = originalHeight / this.dpi;

                            // Calculate how the image should appear on our grid
                            const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                            const canvasWidth = this.fabricCanvas.width;
                            const canvasHeight = this.fabricCanvas.height;

                            // Use a fixed display scale for consistent sizing (100 pixels = 1 inch)
                            const displayScale = 100; // pixels per inch for display

                            console.log(`Sheet: ${sheetWidth}" × ${sheetHeight}"`);
                            console.log(`Canvas: ${canvasWidth} × ${canvasHeight} pixels`);
                            console.log(`Display scale: ${displayScale} pixels per inch`);
                            console.log(`Image real size: ${widthInches.toFixed(3)}" × ${heightInches.toFixed(3)}"`);

                            // Calculate display size based on real-world dimensions
                            const displayWidth = widthInches * displayScale;
                            const displayHeight = heightInches * displayScale;

                            console.log(`Display size should be: ${displayWidth.toFixed(1)} × ${displayHeight.toFixed(1)} pixels`);

                            // Scale the image to show its real size on the grid
                            const scaleX = displayWidth / originalWidth;
                            const scaleY = displayHeight / originalHeight;

                            console.log(`Scale factors: ${scaleX.toFixed(3)} × ${scaleY.toFixed(3)}`);

                            // Position image on grid (snap to grid if desired)
                            const gridSpacing = Math.max(pixelsPerInch, 30);
                            const snapToGrid = true; // Option to snap to grid

                            let left, top;
                            if (snapToGrid) {
                                // Snap to grid intersection
                                left = Math.round(Math.random() * 3) * gridSpacing + gridSpacing;
                                top = Math.round(Math.random() * 3) * gridSpacing + gridSpacing;
                            } else {
                                // Random position but ensure it fits
                                left = Math.random() * (canvasWidth - displayWidth);
                                top = Math.random() * (canvasHeight - displayHeight);
                            }

                            // Set image properties with precise scaling
                            img.set({
                                left: left,
                                top: top,
                                scaleX: scaleX,
                                scaleY: scaleY,
                                selectable: true,
                                hasControls: true,
                                hasBorders: true
                            });

                            this.fabricCanvas.add(img);
                            this.fabricCanvas.setActiveObject(img);

                            // Store precise image data
                            const imageData = {
                                id: Date.now() + Math.random(),
                                name: file.name,
                                url: e.target.result,
                                width: originalWidth,
                                height: originalHeight,
                                widthInches: widthInches,
                                heightInches: heightInches,
                                displayWidth: displayWidth,
                                displayHeight: displayHeight,
                                dpi: this.dpi,
                                fabricObject: img,
                                maxCopies: 1
                            };

                            this.uploadedImages.push(imageData);
                            this.updateImageList();
                            this.enableButtons();
                            this.updateStats();

                            // Show file details and quantity controls
                            this.showFileDetails(file.name, originalWidth, originalHeight, widthInches, heightInches, this.dpi);
                            this.showQuantityControls();

                            this.showStatus(`Added: ${imageData.name} (${widthInches.toFixed(2)}" × ${heightInches.toFixed(2)}")`, 'success');
                            console.log(`✅ Image successfully added to canvas: ${file.name}`);
                            resolve();
                        }, (error) => {
                            console.error(`❌ Fabric.js failed to load image: ${file.name}`, error);
                            reject(new Error(`Failed to load image: ${error}`));
                        });
                    };

                    reader.onerror = (error) => {
                        console.error(`❌ FileReader failed for: ${file.name}`, error);
                        reject(new Error(`Failed to read file: ${error}`));
                    };

                    reader.readAsDataURL(file);
                });
            }

            rotateSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject) {
                    activeObject.rotate(activeObject.angle + 90);
                    this.fabricCanvas.renderAll();
                    this.showStatus('Object rotated 90 degrees', 'success');
                }
            }

            duplicateSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject) {
                    activeObject.clone((cloned) => {
                        cloned.set({
                            left: activeObject.left + 20,
                            top: activeObject.top + 20
                        });
                        this.fabricCanvas.add(cloned);
                        this.fabricCanvas.setActiveObject(cloned);
                        this.showStatus('Object duplicated', 'success');
                    });
                }
            }

            deleteSelected() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (activeObject && !activeObject.isGrid) {
                    this.fabricCanvas.remove(activeObject);
                    this.showStatus('Object deleted', 'success');
                    this.updateStats();
                }
            }

            updateImageList() {
                const list = document.getElementById('image-list');
                const uploadZone = document.getElementById('upload-zone');

                if (this.uploadedImages.length === 0) {
                    list.classList.add('hidden');
                    uploadZone.classList.remove('has-files');
                    this.hideFileDetailsAndQuantity();
                    return;
                }

                uploadZone.classList.add('has-files');
                list.classList.remove('hidden');

                list.innerHTML = this.uploadedImages.map((img, index) => `
                    <div class="image-item">
                        <img src="${img.url}" class="image-thumb" alt="${img.name}">
                        <div class="image-info">
                            <div class="image-name">${img.name}</div>
                            <div class="image-size">${img.width}×${img.height}px</div>
                            <div class="image-real-size">${img.widthInches ? img.widthInches.toFixed(2) : '0.00'}" × ${img.heightInches ? img.heightInches.toFixed(2) : '0.00'}" @ ${img.dpi || 300}DPI</div>
                        </div>
                        <input type="number" class="quantity-input" value="${img.maxCopies}" min="1" max="500"
                               onchange="dtfBuilder.updateImageQuantity(${index}, this.value)">
                    </div>
                `).join('');
            }

            updateImageQuantity(index, quantity) {
                const newQuantity = parseInt(quantity);
                const imageData = this.uploadedImages[index];
                const oldQuantity = imageData.maxCopies;

                // Update the quantity
                imageData.maxCopies = newQuantity;

                // If we have objects on canvas, update them immediately
                const canvasObjects = this.fabricCanvas.getObjects().filter(obj => !obj.isGrid);
                if (canvasObjects.length > 0) {
                    // Re-run auto-arrange to reflect new quantities
                    this.autoNest();
                } else {
                    // Just update stats if no objects on canvas yet
                    this.updateStats();
                }

                this.showStatus(`Updated quantity for ${imageData.name} to ${newQuantity}`, 'success');
            }

            enableButtons() {
                document.getElementById('auto-nest-btn').disabled = false;
                document.getElementById('optimize-btn').disabled = false;
                document.getElementById('preview-btn').disabled = false;
            }

            autoNest() {
                if (this.uploadedImages.length === 0) {
                    this.showStatus('Please add images first', 'error');
                    return;
                }

                this.showStatus('Auto-nesting images...', 'info');

                // Clear existing objects (except grid)
                const objectsToRemove = this.fabricCanvas.getObjects().filter(obj => !obj.isGrid);
                objectsToRemove.forEach(obj => this.fabricCanvas.remove(obj));

                // Professional auto-nesting algorithm
                this.autoArrangeImages();

                document.getElementById('download-btn').classList.remove('hidden');
                this.showStatus('Auto-nesting completed successfully', 'success');
            }

            autoArrangeImages() {
                const canvasWidth = this.fabricCanvas.width;
                const canvasHeight = this.fabricCanvas.height;

                // Convert spacing from mm to pixels
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const pixelsPerInch = Math.min(canvasWidth / sheetWidth, canvasHeight / sheetHeight);
                const spacingPixels = (this.spacing / 25.4) * pixelsPerInch; // Convert mm to inches, then to pixels

                let currentX = spacingPixels;
                let currentY = spacingPixels;
                let rowHeight = 0;
                let totalCopies = 0;

                this.uploadedImages.forEach(imageData => {
                    for (let copy = 0; copy < imageData.maxCopies; copy++) {
                        fabric.Image.fromURL(imageData.url, (img) => {
                            // Use precise scaling based on real dimensions
                            const originalWidth = img.width;
                            const originalHeight = img.height;

                            // Calculate real-world size in inches
                            const widthInches = originalWidth / this.dpi;
                            const heightInches = originalHeight / this.dpi;

                            // Calculate display size based on real-world dimensions
                            const displayWidth = widthInches * pixelsPerInch;
                            const displayHeight = heightInches * pixelsPerInch;

                            // Scale the image to show its real size
                            const scaleX = displayWidth / originalWidth;
                            const scaleY = displayHeight / originalHeight;

                            img.set({
                                scaleX: scaleX,
                                scaleY: scaleY
                            });

                            const imgWidth = img.getScaledWidth();
                            const imgHeight = img.getScaledHeight();

                            // Check if image fits in current row
                            if (currentX + imgWidth > canvasWidth - spacingPixels) {
                                currentX = spacingPixels;
                                currentY += rowHeight + spacingPixels;
                                rowHeight = 0;
                            }

                            // Check if image fits in canvas
                            if (currentY + imgHeight <= canvasHeight - spacingPixels) {
                                img.set({
                                    left: currentX,
                                    top: currentY,
                                    selectable: true,
                                    hasControls: true,
                                    hasBorders: true
                                });

                                this.fabricCanvas.add(img);

                                currentX += imgWidth + spacingPixels;
                                rowHeight = Math.max(rowHeight, imgHeight);
                                totalCopies++;
                            }
                        });
                    }
                });

                // Update stats
                setTimeout(() => {
                    this.updateStats();
                    const efficiency = Math.round((totalCopies * 100) / (canvasWidth * canvasHeight / 10000));
                    document.getElementById('efficiency').textContent = Math.min(efficiency, 100) + '%';
                }, 500);
            }

            updateSheetInfo() {
                const [width, height] = this.sheetSize.split('x');
                document.getElementById('sheet-dimensions').textContent = `${width}" × ${height}"`;
            }

            updateSpacing() {
                // If there are objects on canvas, re-arrange them with new spacing
                const canvasObjects = this.fabricCanvas.getObjects().filter(obj => !obj.isGrid);
                if (canvasObjects.length > 0) {
                    this.autoNest();
                }
                this.showStatus(`Spacing updated to ${this.spacing}mm`, 'success');
            }

            updateStats() {
                document.getElementById('image-count').textContent = this.uploadedImages.length;

                const totalCopies = this.uploadedImages.reduce((sum, img) => sum + img.maxCopies, 0);
                document.getElementById('total-copies').textContent = totalCopies;

                // Count objects on canvas (excluding grid)
                const canvasObjects = this.fabricCanvas.getObjects().filter(obj => !obj.isGrid);
                document.getElementById('total-copies').textContent = canvasObjects.length;
            }

            optimizeLayout() {
                this.showStatus('Optimizing layout for maximum efficiency...', 'info');
                // Advanced optimization would go here
                this.autoNest();
            }

            downloadGangSheet() {
                const link = document.createElement('a');
                link.download = `dtf-gang-sheet-${this.sheetSize}-${this.dpi}dpi.png`;
                link.href = this.fabricCanvas.toDataURL({
                    format: 'png',
                    quality: 1.0
                });
                link.click();

                this.showStatus('Gang sheet downloaded successfully!', 'success');
            }

            async saveProject() {
                if (this.uploadedImages.length === 0) {
                    this.showStatus('Please add images before saving', 'error');
                    return;
                }

                const projectName = prompt('Enter project name:', `DTF Project ${new Date().toLocaleDateString()}`);
                if (!projectName) return;

                const projectData = {
                    name: projectName,
                    sheet_size: this.sheetSize,
                    dpi: this.dpi,
                    spacing: this.spacing,
                    bleed: this.bleed,
                    auto_rotate: this.autoRotate,
                    maintain_aspect: this.maintainAspect,
                    add_margins: this.addMargins,
                    canvas_data: JSON.stringify(this.fabricCanvas.toJSON()),
                    images: this.uploadedImages.map(img => ({
                        name: img.name,
                        width: img.width,
                        height: img.height,
                        max_copies: img.maxCopies
                    }))
                };

                try {
                    const response = await fetch('api/save-project.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(projectData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showStatus(`Project "${projectName}" saved successfully!`, 'success');
                    } else {
                        this.showStatus(`Failed to save project: ${result.message}`, 'error');
                    }
                } catch (error) {
                    this.showStatus('Error saving project. Please try again.', 'error');
                    console.error('Save project error:', error);
                }
            }

            showPreciseMeasurements(obj) {
                if (!obj || obj.isGrid) return;

                // Calculate precise measurements
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.fabricCanvas.width;
                const canvasHeight = this.fabricCanvas.height;
                const pixelsPerInch = Math.min(canvasWidth / sheetWidth, canvasHeight / sheetHeight);

                // Get object dimensions in pixels
                const objWidth = obj.getScaledWidth();
                const objHeight = obj.getScaledHeight();

                // Convert to inches
                const widthInches = objWidth / pixelsPerInch;
                const heightInches = objHeight / pixelsPerInch;

                // Get position in inches
                const leftInches = obj.left / pixelsPerInch;
                const topInches = obj.top / pixelsPerInch;

                // Update status with precise measurements
                const measurementText = `Selected: ${widthInches.toFixed(2)}" × ${heightInches.toFixed(2)}" at (${leftInches.toFixed(2)}", ${topInches.toFixed(2)}")`;
                this.showStatus(measurementText, 'info');
            }

            hidePreciseMeasurements() {
                // Clear measurement display
                document.getElementById('canvas-status').textContent = 'Ready';
            }

            showFileDetails(fileName, pixelWidth, pixelHeight, realWidthInches, realHeightInches, dpi) {
                console.log('showFileDetails called:', fileName, pixelWidth, pixelHeight, realWidthInches, realHeightInches, dpi);

                const fileDetailsDisplay = document.getElementById('file-details-display');
                const fileInfoContent = document.getElementById('file-info-content');

                if (fileDetailsDisplay && fileInfoContent) {
                    fileDetailsDisplay.classList.remove('hidden');

                    fileInfoContent.innerHTML = `
                        <div><strong>File:</strong> ${fileName}</div>
                        <div><strong>Dimensions:</strong> ${pixelWidth} × ${pixelHeight} pixels</div>
                        <div><strong>Size:</strong> ${realWidthInches.toFixed(3)}" × ${realHeightInches.toFixed(3)}"</div>
                        <div><strong>DPI:</strong> ${dpi}</div>
                        <div><strong>Area:</strong> ${(realWidthInches * realHeightInches).toFixed(2)} sq in</div>
                    `;

                    console.log('✅ FILE DETAILS SHOWN:', fileName);
                } else {
                    console.error('❌ FILE DETAILS ELEMENTS NOT FOUND!');
                }
            }

            showQuantityControls() {
                console.log('showQuantityControls called');
                const quantityControls = document.getElementById('quantity-controls');

                if (quantityControls) {
                    quantityControls.classList.remove('hidden');
                    console.log('✅ QUANTITY CONTROLS SHOWN');

                    // Add event listeners for the buttons
                    this.setupQuantityControls();
                } else {
                    console.error('❌ QUANTITY CONTROLS ELEMENT NOT FOUND!');
                }
            }

            setupQuantityControls() {
                // Remove existing listeners to avoid duplicates
                const duplicateBtn = document.getElementById('duplicate-copies-btn');
                const fillSheetBtn = document.getElementById('fill-sheet-btn');

                if (duplicateBtn) {
                    duplicateBtn.onclick = () => this.duplicateWithQuantity();
                }

                if (fillSheetBtn) {
                    fillSheetBtn.onclick = () => this.fillEntireSheet();
                }
            }

            duplicateWithQuantity() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject) {
                    this.showStatus('Please select an image first to duplicate', 'error');
                    return;
                }

                const copiesInput = document.getElementById('copies-input');
                const quantity = copiesInput ? Math.max(1, parseInt(copiesInput.value) || 1) : 1;

                console.log(`Duplicating selected image ${quantity} times`);

                // Get sheet and canvas dimensions for precise positioning
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.fabricCanvas.width;
                const canvasHeight = this.fabricCanvas.height;
                const displayScale = 100; // pixels per inch for display (same as upload)

                // Get object dimensions in inches
                const objWidthPixels = activeObject.getScaledWidth();
                const objHeightPixels = activeObject.getScaledHeight();
                const objWidthInches = objWidthPixels / displayScale;
                const objHeightInches = objHeightPixels / displayScale;

                console.log(`Object size: ${objWidthInches.toFixed(3)}" × ${objHeightInches.toFixed(3)}"`);

                // Calculate how many copies fit per row and column
                const copiesPerRow = Math.floor(sheetWidth / objWidthInches);
                const rowsPerSheet = Math.floor(sheetHeight / objHeightInches);

                console.log(`Grid layout: ${copiesPerRow} per row, ${rowsPerSheet} rows max`);

                // Create copies with precise grid positioning
                let copiesCreated = 0;
                for (let i = 1; i < quantity && copiesCreated < (copiesPerRow * rowsPerSheet - 1); i++) {
                    activeObject.clone((cloned) => {
                        // Calculate grid position
                        const row = Math.floor(i / copiesPerRow);
                        const col = i % copiesPerRow;

                        // Calculate precise position in inches, then convert to pixels
                        const positionXInches = col * objWidthInches;
                        const positionYInches = row * objHeightInches;
                        const positionXPixels = positionXInches * displayScale;
                        const positionYPixels = positionYInches * displayScale;

                        cloned.set({
                            left: positionXPixels,
                            top: positionYPixels
                        });

                        this.fabricCanvas.add(cloned);
                        copiesCreated++;

                        console.log(`Copy ${i} placed at (${positionXInches.toFixed(3)}", ${positionYInches.toFixed(3)})`);
                    });
                }

                this.fabricCanvas.renderAll();
                this.updateStats();
                this.showStatus(`Created ${copiesCreated + 1} copies (${quantity} requested) with precise grid positioning`, 'success');
            }

            fillEntireSheet() {
                const activeObject = this.fabricCanvas.getActiveObject();
                if (!activeObject) {
                    this.showStatus('Please select an image first to fill entire sheet', 'error');
                    return;
                }

                // Get sheet and canvas dimensions for precise calculation
                const [sheetWidth, sheetHeight] = this.sheetSize.split('x').map(Number);
                const canvasWidth = this.fabricCanvas.width;
                const canvasHeight = this.fabricCanvas.height;
                const displayScale = 100; // pixels per inch for display (same as upload)

                // Get object dimensions in inches (real-world size)
                const objWidthPixels = activeObject.getScaledWidth();
                const objHeightPixels = activeObject.getScaledHeight();
                const objWidthInches = objWidthPixels / displayScale;
                const objHeightInches = objHeightPixels / displayScale;

                // Calculate maximum copies based on real sheet dimensions
                const copiesPerRow = Math.floor(sheetWidth / objWidthInches);
                const rowsPerSheet = Math.floor(sheetHeight / objHeightInches);
                const maxCopies = copiesPerRow * rowsPerSheet;

                console.log(`ENTIRE SHEET calculation:`);
                console.log(`Sheet: ${sheetWidth}" × ${sheetHeight}"`);
                console.log(`Object: ${objWidthInches.toFixed(3)}" × ${objHeightInches.toFixed(3)}"`);
                console.log(`Max copies: ${maxCopies} (${copiesPerRow} × ${rowsPerSheet})`);

                // Update quantity input
                const copiesInput = document.getElementById('copies-input');
                if (copiesInput) {
                    copiesInput.value = maxCopies;
                }

                // Create all copies using the duplicate function
                this.duplicateWithQuantity();

                // Calculate efficiency based on real area
                const usedArea = copiesPerRow * objWidthInches * rowsPerSheet * objHeightInches;
                const totalArea = sheetWidth * sheetHeight;
                const efficiency = ((usedArea / totalArea) * 100).toFixed(1);

                this.showStatus(`ENTIRE SHEET: ${maxCopies} copies (${copiesPerRow}×${rowsPerSheet}) = ${efficiency}% efficiency`, 'success');
            }

            showUploadProgress(percentage, message) {
                const uploadProgress = document.getElementById('upload-progress');
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');

                if (uploadProgress && progressBar && progressText) {
                    uploadProgress.classList.remove('hidden');
                    progressBar.style.width = `${percentage}%`;
                    progressText.textContent = message;

                    console.log(`Upload Progress: ${percentage.toFixed(1)}% - ${message}`);
                }
            }

            hideUploadProgress() {
                const uploadProgress = document.getElementById('upload-progress');
                if (uploadProgress) {
                    // Add a small delay to show completion
                    setTimeout(() => {
                        uploadProgress.classList.add('hidden');
                    }, 1000);
                }
            }

            hideFileDetailsAndQuantity() {
                const fileDetailsDisplay = document.getElementById('file-details-display');
                const quantityControls = document.getElementById('quantity-controls');

                if (fileDetailsDisplay) fileDetailsDisplay.classList.add('hidden');
                if (quantityControls) quantityControls.classList.add('hidden');
            }

            showStatus(message, type = 'info') {
                const statusEl = document.getElementById('status-message');
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
                statusEl.classList.remove('hidden');

                document.getElementById('canvas-status').textContent = message;

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    statusEl.classList.add('hidden');
                }, 5000);
            }
        }

        // Initialize the builder
        const dtfBuilder = new ProfessionalDTFBuilder();

        // Global functions for PHP integration
        function loadProject(projectId) {
            // Implementation for loading projects from database
            console.log('Loading project:', projectId);
        }
    </script>
</body>
</html>
