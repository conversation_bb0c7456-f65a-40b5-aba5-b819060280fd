<?php
/**
 * DTF Gang Builder - Load Project API
 * 
 * This endpoint handles loading project data from .dtf files
 */

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // List available projects
        $projects_dir = '../data/projects/';
        $projects = [];
        
        if (is_dir($projects_dir)) {
            $files = glob($projects_dir . '*.dtf');
            foreach ($files as $file) {
                $project_data = json_decode(file_get_contents($file), true);
                if ($project_data) {
                    $projects[] = [
                        'id' => basename($file, '.dtf'),
                        'name' => $project_data['name'] ?? 'Unnamed Project',
                        'description' => $project_data['description'] ?? '',
                        'created' => $project_data['created'] ?? '',
                        'sheet_size' => $project_data['settings']['sheetSize'] ?? '',
                        'total_images' => $project_data['stats']['totalImages'] ?? 0,
                        'total_copies' => $project_data['stats']['totalCopies'] ?? 0,
                        'filename' => basename($file)
                    ];
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'projects' => $projects
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Load specific project
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['project_id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing project_id']);
            exit;
        }
        
        $project_id = htmlspecialchars($input['project_id']);
        $filename = '../data/projects/' . $project_id . '.dtf';
        
        if (!file_exists($filename)) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Project not found']);
            exit;
        }
        
        $project_data = file_get_contents($filename);
        $project_json = json_decode($project_data, true);
        
        if (!$project_json) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Invalid project file']);
            exit;
        }
        
        echo json_encode([
            'success' => true,
            'project' => $project_json
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
