<?php
/**
 * DTF Gang Builder - Simple Save Project API
 * 
 * This endpoint handles saving project data to .dtf files (like reference version)
 */

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
        exit;
    }
    
    // Validate required fields
    $required_fields = ['name', 'sheet_size'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "Missing required field: {$field}"]);
            exit;
        }
    }
    
    // Sanitize input
    $project_name = htmlspecialchars($input['name']);
    $description = htmlspecialchars($input['description'] ?? '');
    $sheet_size = htmlspecialchars($input['sheet_size']);
    
    // Create DTF project format (like reference version)
    $project_data = [
        'name' => $project_name,
        'description' => $description,
        'version' => '1.0',
        'created' => date('c'), // ISO 8601 format
        'settings' => [
            'sheetSize' => $sheet_size,
            'dpi' => $input['dpi'] ?? 300,
            'spacing' => ($input['spacing'] ?? 3) / 25.4, // Convert mm to inches
            'bleed' => ($input['bleed'] ?? 1) / 25.4, // Convert mm to inches
            'autoRotate' => $input['auto_rotate'] ?? true,
            'maintainAspect' => $input['maintain_aspect'] ?? true,
            'addMargins' => $input['add_margins'] ?? false,
            'gridSize' => 1,
            'gridColor' => '#cccccc',
            'showGrid' => true,
            'snapToGrid' => false
        ],
        'images' => $input['images'] ?? [],
        'layout' => $input['layout'] ?? [],
        'stats' => [
            'totalImages' => count($input['images'] ?? []),
            'totalCopies' => array_sum(array_column($input['images'] ?? [], 'maxCopies')),
            'efficiency' => $input['efficiency'] ?? 0
        ]
    ];
    
    // Create projects directory if it doesn't exist
    $projects_dir = '../data/projects/';
    if (!is_dir($projects_dir)) {
        mkdir($projects_dir, 0755, true);
    }
    
    // Generate unique filename
    $project_id = uniqid('dtf_', true);
    $filename = $projects_dir . $project_id . '.dtf';
    
    // Save project data
    if (file_put_contents($filename, json_encode($project_data, JSON_PRETTY_PRINT))) {
        echo json_encode([
            'success' => true,
            'message' => 'Project saved successfully',
            'project_id' => $project_id,
            'filename' => basename($filename),
            'download_url' => 'data/projects/' . basename($filename)
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to save project']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}
?>
